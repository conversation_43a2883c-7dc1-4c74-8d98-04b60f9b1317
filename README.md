# Bible Companion

A modern, cross-platform Bible companion app built with Expo Router v2, React Native, and TypeScript.

## Features

- 📱 **Cross-platform**: Runs on iOS, Android, and Web
- 🎨 **Modern UI**: Clean, responsive design with dark/light theme support
- 🔒 **TypeScript**: Strict TypeScript configuration for type safety
- 🛣️ **Expo Router v2**: File-based routing with typed routes
- 🎯 **ESLint**: Airbnb-style linting rules for consistent code quality
- 💅 **Prettier**: Automatic code formatting
- 🪝 **Husky**: Pre-commit hooks for code quality
- 📝 **Semantic Commits**: Conventional commit messages with changelog generation

## Tech Stack

- **Framework**: Expo SDK 51+ with Expo Router v2
- **Language**: TypeScript (strict mode)
- **Styling**: React Native StyleSheet with theme system
- **Linting**: ESLint with Airbnb TypeScript configuration
- **Formatting**: Prettier
- **Git Hooks**: Husky with lint-staged
- **Commit Convention**: Conventional Commits with commitlint

## Project Structure

```
bible-companion/
├── app/                    # Expo Router v2 app directory
│   ├── _layout.tsx        # Root layout with ThemeProvider
│   ├── index.tsx          # Home screen
│   └── login.tsx          # Login screen
├── src/                   # Source code
│   ├── components/        # Reusable components
│   │   └── ThemeProvider.tsx
│   ├── hooks/            # Custom hooks
│   │   └── useTheme.ts
│   ├── theme/            # Theme configuration
│   │   ├── colors.ts
│   │   └── index.ts
│   └── types/            # TypeScript type definitions
│       └── theme.ts
├── assets/               # Static assets (icons, images)
├── app.json             # Expo configuration
├── tsconfig.json        # TypeScript configuration (strict)
├── eslint.config.js     # ESLint configuration (Airbnb)
├── babel.config.js      # Babel configuration
└── metro.config.js      # Metro bundler configuration
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)

### Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd bible-companion
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your Firebase and MongoDB credentials
   ```

4. Start the development server:
   ```bash
   npm start
   ```

### Production Infrastructure

For production deployment, see [INFRASTRUCTURE_SETUP.md](./INFRASTRUCTURE_SETUP.md) for setting up:
- MongoDB Atlas cluster
- Cloudflare R2 storage
- GitHub CI/CD secrets

### Available Scripts

- `npm start` - Start Expo development server
- `npm run web` - Start web development server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier
- `npm test` - Run tests (placeholder)

## Development

### Code Quality

This project enforces strict code quality standards:

- **TypeScript**: Strict mode enabled with comprehensive type checking
- **ESLint**: Airbnb TypeScript rules with React Native specific rules
- **Prettier**: Automatic code formatting on save and pre-commit
- **Husky**: Pre-commit hooks run linting and formatting
- **Commitlint**: Enforces conventional commit message format

### Theme System

The app includes a comprehensive theme system with:

- Light and dark mode support
- Automatic system theme detection
- Consistent color palette
- Typography scale
- Spacing system
- Border radius tokens

### Routing

Uses Expo Router v2 with:

- File-based routing
- Typed routes (experimental)
- Stack navigation
- Modal presentations

## Contributing

1. Follow conventional commit format: `type(scope): description`
2. Ensure all linting passes: `npm run lint`
3. Format code: `npm run format`
4. Test on multiple platforms before submitting PR

## License

MIT License
