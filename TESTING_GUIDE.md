# 🧪 Testing Guide - Bible Companion

## 📊 Current Testing Status

### ✅ **Implemented**
- **Jest** - Core testing framework
- **TypeScript Support** - Type checking in tests
- **Basic Configuration** - Jest, Babel, and test scripts
- **Theme Tests** - Complete coverage of theme system
- **Mock Setup** - Expo and React Native mocks

### 🔄 **In Progress**
- **Component Tests** - Created but need React Testing Library
- **Screen Tests** - Created but need React Testing Library
- **Hook Tests** - Created but need React Testing Library

### ❌ **Missing**
- **React Native Testing Library** - Component testing utilities
- **Integration Tests** - End-to-end testing
- **Snapshot Tests** - UI regression testing

## 🚀 Available Test Scripts

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci

# Run specific test file
npm test src/theme/__tests__/colors.test.ts

# Run tests matching pattern
npm test src/theme
```

## 📁 Test Structure

```
bible-companion/
├── src/
│   ├── __tests__/           # General utility tests
│   │   └── setup.test.ts    # Jest configuration test
│   ├── components/
│   │   └── __tests__/       # Component tests
│   ├── hooks/
│   │   └── __tests__/       # Hook tests
│   └── theme/
│       └── __tests__/       # Theme tests (WORKING)
├── app/
│   └── __tests__/           # Screen tests
├── jest.config.js           # Jest configuration
├── jest.setup.js            # Test setup and mocks
└── babel.config.js          # Babel configuration
```

## 🔧 Configuration Files

### Jest Configuration (`jest.config.js`)
- **Environment**: jsdom for React components
- **TypeScript**: Full TypeScript support
- **Module Mapping**: Path aliases (@/ imports)
- **Coverage**: 70% threshold for all metrics
- **Transform**: Babel for JS/TS/JSX/TSX files

### Test Setup (`jest.setup.js`)
- **Expo Mocks**: expo-router, expo-constants, expo-linking
- **React Native Mocks**: Alert, UIManager, Animated
- **Global Setup**: __DEV__ flag, console mocking

## ✅ Working Tests

### Theme System Tests
```bash
npm test src/theme
```

**Coverage:**
- ✅ Color validation (light/dark themes)
- ✅ Theme structure verification
- ✅ Typography configuration
- ✅ Spacing and border radius
- ✅ Hex color format validation

## 🔄 Pending Tests (Need React Testing Library)

### Component Tests
- **ThemeProvider**: Context provider functionality
- **Custom Hooks**: useTheme hook behavior

### Screen Tests
- **Home Screen**: Rendering, navigation, user interactions
- **Login Screen**: Form validation, submission, error handling

## 📋 Next Steps

### Phase 1: Install React Testing Library
```bash
npm install --save-dev @testing-library/react-native @testing-library/jest-native
```

### Phase 2: Update Test Files
- Fix component test imports
- Add fireEvent and render utilities
- Update mock configurations

### Phase 3: Expand Test Coverage
- Add integration tests
- Implement snapshot testing
- Add accessibility testing
- Create performance tests

## 🎯 Testing Best Practices

### 1. **Test Structure**
```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup before each test
  });

  it('should render correctly', () => {
    // Test implementation
  });

  it('should handle user interactions', () => {
    // Test user interactions
  });
});
```

### 2. **Naming Conventions**
- Test files: `*.test.ts` or `*.test.tsx`
- Test directories: `__tests__/`
- Describe blocks: Component/function name
- Test cases: "should [expected behavior]"

### 3. **Mock Strategy**
- Mock external dependencies
- Mock React Native components
- Mock Expo modules
- Keep mocks simple and focused

### 4. **Coverage Goals**
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## 🐛 Troubleshooting

### Common Issues

1. **Babel Configuration Error**
   - Remove deprecated expo-router/babel plugin
   - Use babel-preset-expo only

2. **Module Resolution**
   - Check moduleNameMapper in jest.config.js
   - Verify path aliases match tsconfig.json

3. **React Native Mocks**
   - Update jest.setup.js for missing modules
   - Add specific component mocks as needed

### Debug Commands
```bash
# Run tests with verbose output
npm test -- --verbose

# Run tests with debug info
npm test -- --debug

# Clear Jest cache
npx jest --clearCache
```

## 📈 Coverage Reports

Coverage reports are generated in the `coverage/` directory:
- **HTML Report**: `coverage/lcov-report/index.html`
- **Text Report**: Console output
- **LCOV Report**: `coverage/lcov.info`

## 🎯 Testing Priorities

### High Priority
1. ✅ Theme system (COMPLETE)
2. 🔄 Component rendering
3. 🔄 User interactions
4. 🔄 Navigation flow

### Medium Priority
5. ❌ Error handling
6. ❌ Accessibility
7. ❌ Performance
8. ❌ Integration tests

### Low Priority
9. ❌ Snapshot tests
10. ❌ E2E tests
11. ❌ Visual regression
12. ❌ Load testing

---

**🎉 Testing foundation is established! Ready for component testing implementation.**
