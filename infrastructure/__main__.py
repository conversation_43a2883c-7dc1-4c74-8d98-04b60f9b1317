"""
Bible Companion Infrastructure - Pulumi Program

This program provisions the core infrastructure for the Bible Companion app:
- MongoDB Atlas M10 cluster
- Cloudflare R2 bucket for verse media
- GitHub secrets for CI/CD

Prerequisites:
- pulumi config set mongodbatlas:publicKey <your-atlas-public-key>
- pulumi config set mongodbatlas:privateKey <your-atlas-private-key> --secret
- pulumi config set bible-companion:dbPassword <strong-password> --secret
- pulumi config set cloudflare:accountId <your-cloudflare-account-id>
- pulumi config set cloudflare:apiToken <your-cloudflare-api-token> --secret
- pulumi config set github:token <your-github-token> --secret
"""

import pulumi
import pulumi_mongodbatlas as mongodbatlas
import pulumi_cloudflare as cloudflare
import pulumi_github as github
import secrets
import string

# Get configuration
config = pulumi.Config()
bible_config = pulumi.Config("bible-companion")

# MongoDB Atlas configuration
atlas_public_key = config.get("mongodbatlas:publicKey")
atlas_private_key = config.get_secret("mongodbatlas:privateKey")
atlas_org_id = config.get("mongodbatlas:orgId")
db_password = bible_config.get_secret("dbPassword")

# Cloudflare configuration  
cf_account_id = config.get("cloudflare:accountId")
cf_api_token = config.get_secret("cloudflare:apiToken")

# GitHub configuration
github_token = config.get_secret("github:token")
github_repo = bible_config.get("githubRepo") or "Jpkay/bible"

# Validate required configuration
def validate_config():
    """Validate that all required configuration is present"""
    errors = []
    
    if not atlas_public_key:
        errors.append("Missing mongodbatlas:publicKey - run: pulumi config set mongodbatlas:publicKey <your-key>")
    
    if not atlas_private_key:
        errors.append("Missing mongodbatlas:privateKey - run: pulumi config set mongodbatlas:privateKey <your-key> --secret")

    if not atlas_org_id:
        errors.append("Missing mongodbatlas:orgId - run: pulumi config set mongodbatlas:orgId <your-org-id>")

    if not cf_account_id:
        errors.append("Missing cloudflare:accountId - run: pulumi config set cloudflare:accountId <your-account-id>")
    
    if not cf_api_token:
        errors.append("Missing cloudflare:apiToken - run: pulumi config set cloudflare:apiToken <your-token> --secret")
    
    if not github_token:
        errors.append("Missing github:token - run: pulumi config set github:token <your-token> --secret")
    
    if errors:
        raise Exception(f"Configuration errors:\n" + "\n".join(f"  - {error}" for error in errors))

# Validate configuration before proceeding
validate_config()

# Generate strong password if not provided
def generate_password(length=16):
    """Generate a strong password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

# Use provided password or generate one
actual_db_password = db_password or generate_password()

# =============================================================================
# MongoDB Atlas Resources
# =============================================================================

# Create MongoDB Atlas project
atlas_project = mongodbatlas.Project(
    "bible-companion-prod",
    name="bible-companion-prod",
    org_id=atlas_org_id,  # This needs to be set via config
    opts=pulumi.ResourceOptions(
        protect=True  # Protect against accidental deletion
    )
)

# Create M10 cluster in eu-central-1
atlas_cluster = mongodbatlas.Cluster(
    "Cluster0",
    project_id=atlas_project.id,
    name="Cluster0",
    
    # M10 cluster configuration
    cluster_type="REPLICASET",
    mongo_db_major_version="7.0",
    
    # Provider settings for AWS eu-central-1
    provider_name="AWS",
    provider_instance_size_name="M10",
    provider_region_name="EU_CENTRAL_1",
    
    # Enable backup
    backup_enabled=True,
    auto_scaling_disk_gb_enabled=True,
    
    opts=pulumi.ResourceOptions(
        depends_on=[atlas_project],
        protect=True
    )
)

# Create database user
atlas_user = mongodbatlas.DatabaseUser(
    "bc-app-user",
    username="bc_app",
    password=actual_db_password,
    project_id=atlas_project.id,
    auth_database_name="admin",
    
    # Grant readWriteAnyDatabase role
    roles=[
        mongodbatlas.DatabaseUserRoleArgs(
            role_name="readWriteAnyDatabase",
            database_name="admin"
        )
    ],
    
    opts=pulumi.ResourceOptions(
        depends_on=[atlas_cluster]
    )
)

# Create IP whitelist (temporary - allows all IPs)
atlas_ip_whitelist = mongodbatlas.ProjectIpAccessList(
    "temp-allow-all",
    project_id=atlas_project.id,
    cidr_block="0.0.0.0/0",
    comment="Temporary - allow all IPs for development",
    
    opts=pulumi.ResourceOptions(
        depends_on=[atlas_project]
    )
)

# =============================================================================
# Cloudflare R2 Bucket
# =============================================================================

# Create R2 bucket for verse media
r2_bucket = cloudflare.R2Bucket(
    "verse-media",
    account_id=cf_account_id,
    name="verse-media",
    location="auto",  # Let Cloudflare choose optimal location
    
    opts=pulumi.ResourceOptions(
        protect=True
    )
)

# =============================================================================
# GitHub Secrets
# =============================================================================

# Build MongoDB connection string
mongo_connection_string = pulumi.Output.all(
    atlas_cluster.connection_strings,
    actual_db_password
).apply(lambda args: 
    args[0][0].standard_srv.replace(
        "<password>", args[1]
    ).replace(
        "<username>", "bc_app"
    )
)

# Create GitHub repository secrets
github_mongo_secret = github.ActionsSecret(
    "mongo-uri-secret",
    repository=github_repo,
    secret_name="MONGO_URI",
    plaintext_value=mongo_connection_string,
    
    opts=pulumi.ResourceOptions(
        depends_on=[atlas_user]
    )
)

# For R2, we'll use environment variables instead of API tokens for simplicity
github_r2_access_key_secret = github.ActionsSecret(
    "r2-access-key-secret", 
    repository=github_repo,
    secret_name="R2_ACCESS_KEY",
    plaintext_value="YOUR_R2_ACCESS_KEY",  # User needs to set this manually
    
    opts=pulumi.ResourceOptions(
        depends_on=[r2_bucket]
    )
)

github_r2_secret_key_secret = github.ActionsSecret(
    "r2-secret-key-secret",
    repository=github_repo, 
    secret_name="R2_SECRET_KEY",
    plaintext_value="YOUR_R2_SECRET_KEY",  # User needs to set this manually
    
    opts=pulumi.ResourceOptions(
        depends_on=[r2_bucket]
    )
)

# =============================================================================
# Outputs
# =============================================================================

# Export important values
pulumi.export("cluster_connection_string", mongo_connection_string)
pulumi.export("r2_bucket_url", r2_bucket.name.apply(lambda name: f"https://{name}.r2.cloudflarestorage.com"))
pulumi.export("atlas_project_id", atlas_project.id)
pulumi.export("atlas_cluster_name", atlas_cluster.name)
pulumi.export("r2_bucket_name", r2_bucket.name)

# Export setup instructions
pulumi.export("setup_instructions", pulumi.Output.all(
    mongo_connection_string,
    r2_bucket.name
).apply(lambda args: f"""
🎉 Infrastructure provisioned successfully!

Next steps:
1. Update your local .env file with:
   EXPO_PUBLIC_MONGODB_URI={args[0]}
   
2. Configure R2 access:
   - Create R2 API tokens in Cloudflare dashboard
   - Update GitHub secrets: R2_ACCESS_KEY, R2_SECRET_KEY
   - Bucket: {args[1]}
   
3. Security recommendations:
   - Replace IP whitelist 0.0.0.0/0 with specific IPs
   - Rotate API keys regularly
   - Enable MongoDB Atlas network security
   
4. Monitor your resources:
   - MongoDB Atlas: https://cloud.mongodb.com/
   - Cloudflare R2: https://dash.cloudflare.com/
"""))
