#!/bin/bash

# Bible Companion Infrastructure Setup Script
# This script helps configure Pulumi for the Bible Companion infrastructure

set -e

echo "🚀 Bible Companion Infrastructure Setup"
echo "======================================="

# Check if Pulumi is installed
if ! command -v pulumi &> /dev/null; then
    echo "❌ Pulumi CLI not found. Installing..."
    curl -fsSL https://get.pulumi.com | sh
    export PATH=$PATH:$HOME/.pulumi/bin
fi

echo "✅ Pulumi CLI found"

# Check if we're in the right directory
if [ ! -f "Pulumi.yaml" ]; then
    echo "❌ Please run this script from the infrastructure directory"
    exit 1
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Initialize stack if it doesn't exist
if ! pulumi stack ls | grep -q "prod"; then
    echo "🏗️  Initializing Pulumi stack..."
    pulumi stack init prod
else
    echo "✅ Pulumi stack 'prod' already exists"
    pulumi stack select prod
fi

echo ""
echo "🔧 Configuration Setup"
echo "======================"

# MongoDB Atlas configuration
echo ""
echo "📊 MongoDB Atlas Configuration"
echo "------------------------------"
echo "Get your API keys from: https://cloud.mongodb.com/v2#/org/YOUR_ORG_ID/access/apiKeys"

read -p "Enter MongoDB Atlas Public Key: " atlas_public_key
pulumi config set mongodbatlas:publicKey "$atlas_public_key"

read -s -p "Enter MongoDB Atlas Private Key: " atlas_private_key
echo ""
pulumi config set mongodbatlas:privateKey "$atlas_private_key" --secret

echo "Get your Organization ID from the Atlas URL: https://cloud.mongodb.com/v2#/org/{ORG_ID}/projects"
read -p "Enter MongoDB Atlas Organization ID: " atlas_org_id
pulumi config set mongodbatlas:orgId "$atlas_org_id"

read -s -p "Enter Database Password (leave empty to auto-generate): " db_password
echo ""
if [ ! -z "$db_password" ]; then
    pulumi config set bible-companion:dbPassword "$db_password" --secret
fi

# Cloudflare configuration
echo ""
echo "☁️  Cloudflare Configuration"
echo "----------------------------"
echo "Get your Account ID from: https://dash.cloudflare.com/"

read -p "Enter Cloudflare Account ID: " cf_account_id
pulumi config set cloudflare:accountId "$cf_account_id"

echo "Create an API token with R2:Edit permissions at: https://dash.cloudflare.com/profile/api-tokens"
read -s -p "Enter Cloudflare API Token: " cf_api_token
echo ""
pulumi config set cloudflare:apiToken "$cf_api_token" --secret

# GitHub configuration
echo ""
echo "🐙 GitHub Configuration"
echo "-----------------------"
echo "Create a token with 'repo' and 'admin:repo_hook' scopes at: https://github.com/settings/tokens"

read -s -p "Enter GitHub Personal Access Token: " github_token
echo ""
pulumi config set github:token "$github_token" --secret

read -p "Enter GitHub Repository (owner/repo) [default: Jpkay/bible]: " github_repo
if [ ! -z "$github_repo" ]; then
    pulumi config set bible-companion:githubRepo "$github_repo"
fi

echo ""
echo "✅ Configuration complete!"
echo ""
echo "🚀 Ready to deploy!"
echo "==================="
echo ""
echo "To deploy the infrastructure, run:"
echo "  pulumi up"
echo ""
echo "To view the configuration:"
echo "  pulumi config"
echo ""
echo "To view outputs after deployment:"
echo "  pulumi stack output"
echo ""
echo "📖 For more information, see README.md"
