{"name": "bible-companion", "version": "1.0.0", "main": "expo-router/entry", "repository": "https://x-access-token:<EMAIL>/Jpkay/bible.git", "author": "CodeSandbox <<EMAIL>>", "license": "MIT", "scripts": {"start": "expo start", "web": "expo start --web", "prepare": "husky install", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@expo/metro-config": "^0.20.14", "@react-native-community/eslint-config": "^3.2.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "^18.3.23", "@types/react-native": "^0.73.0", "@types/react-test-renderer": "^19.1.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.27.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^16.1.0", "metro": "^0.82.4", "prettier": "^3.5.3", "react-test-renderer": "^18.2.0", "typescript": "^5.8.3"}, "dependencies": {"@expo/cli": "^0.24.13", "@expo/metro-runtime": "^3.2.3", "@react-native-async-storage/async-storage": "^1.24.0", "expo": "^53.0.9", "expo-apple-authentication": "^7.2.4", "expo-auth-session": "^6.1.5", "expo-constants": "^16.0.2", "expo-linking": "^6.3.1", "expo-local-authentication": "^16.0.4", "expo-router": "^3.5.24", "expo-secure-store": "^14.2.3", "expo-status-bar": "^1.12.1", "firebase": "^11.8.1", "mongodb": "^6.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-native": "^0.74.5", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "^3.31.1", "react-native-web": "^0.19.12"}}