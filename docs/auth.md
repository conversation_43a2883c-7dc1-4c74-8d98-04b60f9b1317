# Authentication Setup Guide

This guide covers the setup and configuration for all authentication methods supported by the Bible Companion app.

## Overview

The app supports multiple authentication methods:
- **Email/Password**: Traditional email and password authentication
- **Phone/SMS**: Phone number verification with OTP
- **Google Sign-In**: OAuth authentication with Google
- **<PERSON> Sign-In**: OAuth authentication with Apple (iOS only)
- **Biometric Authentication**: Fingerprint/Face ID for quick access

## Prerequisites

- Expo SDK ≥ 50
- Firebase project with Authentication enabled
- MongoDB database for user data storage

## Firebase Configuration

### 1. Basic Setup

Ensure your Firebase project has the following authentication providers enabled:

1. Go to Firebase Console → Authentication → Sign-in method
2. Enable the following providers:
   - Email/Password
   - Phone
   - Google
   - Apple (if targeting iOS)

### 2. Environment Variables

Update your `.env` file with the following configuration:

```env
# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
EXPO_PUBLIC_FIREBASE_APP_ID=your-app-id

# Social Authentication
EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS=your-google-ios-client-id
EXPO_PUBLIC_GOOGLE_CLIENT_ID_ANDROID=your-google-android-client-id
EXPO_PUBLIC_GOOGLE_CLIENT_ID_WEB=your-google-web-client-id
EXPO_PUBLIC_APPLE_CLIENT_ID=your-apple-client-id

# Security Configuration
EXPO_PUBLIC_ENABLE_BIOMETRIC_AUTH=true
```

## Phone Authentication Setup

### Web Platform (reCAPTCHA)

For web platforms, Firebase uses reCAPTCHA for phone verification:

1. **reCAPTCHA v3 (Invisible)**:
   - Automatically configured for production
   - Provides seamless user experience
   - Falls back to v2 if needed

2. **reCAPTCHA v2 (Widget)**:
   - Used as fallback when v3 fails
   - Requires user interaction
   - Automatically rendered in the app

### Native Platforms

For React Native platforms, phone authentication requires additional backend setup:

1. **Firebase Admin SDK**: Set up a backend service with Firebase Admin SDK
2. **Cloud Functions**: Use Firebase Cloud Functions for OTP generation
3. **Custom Backend**: Implement your own OTP service

**Note**: The current implementation includes a placeholder for native phone auth that requires backend implementation.

## Google Sign-In Setup

### 1. Firebase Console Configuration

1. Go to Firebase Console → Authentication → Sign-in method
2. Enable Google provider
3. Add your app's SHA-1 fingerprint (for Android)
4. Download the updated `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)

### 2. Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your Firebase project
3. Navigate to APIs & Services → Credentials
4. Create OAuth 2.0 client IDs for:
   - **iOS application**: Use your app's bundle identifier
   - **Android application**: Use your app's package name and SHA-1 fingerprint
   - **Web application**: For web platform support

### 3. Client ID Configuration

Update your `.env` file with the appropriate client IDs:

```env
EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS=your-ios-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_CLIENT_ID_ANDROID=your-android-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_CLIENT_ID_WEB=your-web-client-id.apps.googleusercontent.com
```

## Apple Sign-In Setup

### 1. Apple Developer Account

1. Go to [Apple Developer Portal](https://developer.apple.com/)
2. Navigate to Certificates, Identifiers & Profiles
3. Create an App ID with Sign In with Apple capability
4. Create a Services ID for your app

### 2. Firebase Configuration

1. In Firebase Console → Authentication → Sign-in method
2. Enable Apple provider
3. Add your Services ID and Team ID
4. Upload your Apple Sign-In private key

### 3. App Configuration

Apple Sign-In is automatically available on iOS 13+ devices with the following requirements:

- **iOS 13+**: Required for Apple Sign-In
- **Automatic fallback**: Shows appropriate message on unsupported devices
- **Privacy compliance**: Follows Apple's privacy guidelines

## Biometric Authentication Setup

### 1. Device Requirements

- **iOS**: Touch ID or Face ID enabled device
- **Android**: Fingerprint or face unlock enabled device

### 2. Security Implementation

The biometric authentication system:

1. **Token Storage**: Uses Expo SecureStore for secure token storage
2. **Fallback Options**: Provides passcode fallback when biometrics fail
3. **Token Refresh**: Automatically handles token expiration

### 3. User Flow

1. User signs in with any method (email, phone, social)
2. App prompts to enable biometric authentication
3. User's authentication token is securely stored
4. Future sign-ins use biometric verification
5. Fallback to other methods if biometrics fail

## Usage Examples

### Basic Authentication

```typescript
import { useAuth } from '../src/contexts/AuthContext';

const { 
  signInWithEmail, 
  signInWithGoogle, 
  signInWithApple, 
  signInWithBiometrics,
  enableBiometrics 
} = useAuth();

// Email sign-in
await signInWithEmail('<EMAIL>', 'password');

// Google sign-in
await signInWithGoogle();

// Apple sign-in (iOS only)
await signInWithApple();

// Enable biometrics after sign-in
const enabled = await enableBiometrics();

// Biometric sign-in
await signInWithBiometrics();
```

### Phone Authentication

```typescript
// Send OTP
const verificationId = await signInWithPhone('+1234567890');

// Verify OTP
await verifyPhoneOTP(verificationId, '123456', userProfile);
```

## Security Considerations

### 1. Token Management

- **Secure Storage**: All sensitive tokens are stored using Expo SecureStore
- **Token Rotation**: Implement regular token refresh for enhanced security
- **Biometric Binding**: Tokens are bound to device biometric authentication
- **Firebase Integration**: Seamless integration with Firebase Authentication
- **MongoDB Sync**: User data automatically synced with MongoDB collections

### 2. Error Handling

- **Graceful Degradation**: App continues to function if specific auth methods fail
- **User Feedback**: Clear error messages for authentication failures
- **Retry Logic**: Automatic retry for transient failures

### 3. Privacy Compliance

- **Data Minimization**: Only collect necessary user information
- **Consent Management**: Clear consent for biometric data usage
- **Apple Guidelines**: Full compliance with Apple's Sign-In requirements

## Testing

### Running Tests

```bash
# Run all authentication tests
npm test auth

# Run enhanced authentication tests
npm test auth-enhanced

# Run with coverage
npm run test:coverage
```

### Test Coverage

The test suite covers:
- ✅ Email authentication (sign-in/sign-up)
- ✅ Phone authentication with OTP verification
- ✅ Google Sign-In (native and web)
- ✅ Apple Sign-In (iOS)
- ✅ Biometric authentication setup and usage
- ✅ Error handling for all methods
- ✅ Anonymous user linking for phone auth

## Troubleshooting

### Common Issues

1. **Google Sign-In Fails**:
   - Verify client IDs are correctly configured
   - Check SHA-1 fingerprints for Android
   - Ensure Firebase project is properly linked

2. **Apple Sign-In Not Available**:
   - Verify iOS version is 13+
   - Check Apple Developer account configuration
   - Ensure proper Services ID setup

3. **Biometric Authentication Fails**:
   - Verify device has biometric hardware
   - Check if biometric credentials are enrolled
   - Ensure secure storage permissions

4. **Phone Authentication Issues**:
   - Verify reCAPTCHA configuration for web
   - Check Firebase quota limits
   - Ensure proper phone number formatting

### Debug Mode

Enable debug logging by setting:

```env
EXPO_PUBLIC_ENABLE_DEBUG_LOGS=true
EXPO_PUBLIC_LOG_LEVEL=debug
```

## Support

For additional support:
- Check Firebase documentation
- Review Expo authentication guides
- Consult platform-specific documentation (Apple, Google)
- Check the app's GitHub repository for issues and updates
