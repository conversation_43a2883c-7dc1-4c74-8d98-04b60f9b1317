import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Import translation files
import en from './locales/en.json';
import rw from './locales/rw.json';
import sw from './locales/sw.json';
import fr from './locales/fr.json';

// Language resources
const resources = {
  en: { translation: en },
  rw: { translation: rw },
  sw: { translation: sw },
  fr: { translation: fr },
};

// Supported languages
export const supportedLanguages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'rw', name: 'Kinyarwanda', nativeName: 'Ikinyarwanda' },
  { code: 'sw', name: 'Swahili', nativeName: 'Kiswahili' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
];

// Storage key for language preference
const LANGUAGE_STORAGE_KEY = 'bc.locale';

// Language detection function
const detectLanguage = async (): Promise<string> => {
  try {
    // First, try to get saved language preference
    const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    if (savedLanguage && supportedLanguages.some(lang => lang.code === savedLanguage)) {
      return savedLanguage;
    }

    // Fall back to device locale
    const deviceLocale = Localization.locale;
    const deviceLanguageCode = deviceLocale.split('-')[0]; // Get language code without region

    // Check if device language is supported
    if (supportedLanguages.some(lang => lang.code === deviceLanguageCode)) {
      return deviceLanguageCode;
    }

    // Default to English
    return 'en';
  } catch (error) {
    console.warn('Error detecting language:', error);
    return 'en';
  }
};

// Save language preference
export const saveLanguagePreference = async (languageCode: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
  } catch (error) {
    console.warn('Error saving language preference:', error);
  }
};

// Initialize i18n
const initI18n = async (): Promise<void> => {
  const detectedLanguage = await detectLanguage();

  // For web, we can use browser language detector
  if (Platform.OS === 'web') {
    const LanguageDetector = await import('i18next-browser-languagedetector');
    
    i18n
      .use(LanguageDetector.default)
      .use(initReactI18next)
      .init({
        resources,
        lng: detectedLanguage,
        fallbackLng: 'en',
        debug: __DEV__,
        interpolation: {
          escapeValue: false,
        },
        react: {
          useSuspense: false,
        },
        detection: {
          order: ['localStorage', 'navigator'],
          caches: ['localStorage'],
          lookupLocalStorage: LANGUAGE_STORAGE_KEY,
        },
      });
  } else {
    // For React Native
    i18n
      .use(initReactI18next)
      .init({
        resources,
        lng: detectedLanguage,
        fallbackLng: 'en',
        debug: __DEV__,
        interpolation: {
          escapeValue: false,
        },
        react: {
          useSuspense: false,
        },
      });
  }

  // Listen for language changes and save preference
  i18n.on('languageChanged', (lng: string) => {
    saveLanguagePreference(lng);
  });
};

// Initialize i18n
initI18n();

export default i18n;
