import { lightColors, darkColors } from '../colors';

describe('Theme Colors', () => {
  describe('lightColors', () => {
    it('should have all required color properties', () => {
      expect(lightColors).toHaveProperty('primary');
      expect(lightColors).toHaveProperty('secondary');
      expect(lightColors).toHaveProperty('background');
      expect(lightColors).toHaveProperty('surface');
      expect(lightColors).toHaveProperty('text');
      expect(lightColors).toHaveProperty('textSecondary');
      expect(lightColors).toHaveProperty('border');
      expect(lightColors).toHaveProperty('error');
      expect(lightColors).toHaveProperty('warning');
      expect(lightColors).toHaveProperty('success');
    });

    it('should have valid hex color values', () => {
      const hexColorRegex = /^#[0-9A-F]{6}$/i;

      expect(lightColors.primary).toMatch(hexColorRegex);
      expect(lightColors.secondary).toMatch(hexColorRegex);
      expect(lightColors.background).toMatch(hexColorRegex);
      expect(lightColors.surface).toMatch(hexColorRegex);
      expect(lightColors.text).toMatch(hexColorRegex);
      expect(lightColors.textSecondary).toMatch(hexColorRegex);
    });
  });

  describe('darkColors', () => {
    it('should have all required color properties', () => {
      expect(darkColors).toHaveProperty('primary');
      expect(darkColors).toHaveProperty('secondary');
      expect(darkColors).toHaveProperty('background');
      expect(darkColors).toHaveProperty('surface');
      expect(darkColors).toHaveProperty('text');
      expect(darkColors).toHaveProperty('textSecondary');
      expect(darkColors).toHaveProperty('border');
      expect(darkColors).toHaveProperty('error');
      expect(darkColors).toHaveProperty('warning');
      expect(darkColors).toHaveProperty('success');
    });

    it('should have different colors from light theme', () => {
      expect(darkColors.primary).not.toBe(lightColors.primary);
      expect(darkColors.background).not.toBe(lightColors.background);
      expect(darkColors.text).not.toBe(lightColors.text);
    });

    it('should have valid hex color values', () => {
      const hexColorRegex = /^#[0-9A-F]{6}$/i;

      expect(darkColors.primary).toMatch(hexColorRegex);
      expect(darkColors.secondary).toMatch(hexColorRegex);
      expect(darkColors.background).toMatch(hexColorRegex);
      expect(darkColors.surface).toMatch(hexColorRegex);
      expect(darkColors.text).toMatch(hexColorRegex);
      expect(darkColors.textSecondary).toMatch(hexColorRegex);
    });
  });
});
