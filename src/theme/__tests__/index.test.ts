import { lightTheme, darkTheme } from '../index';

describe('Theme Configuration', () => {
  describe('lightTheme', () => {
    it('should have all required theme properties', () => {
      expect(lightTheme).toHaveProperty('colors');
      expect(lightTheme).toHaveProperty('spacing');
      expect(lightTheme).toHaveProperty('borderRadius');
      expect(lightTheme).toHaveProperty('typography');
    });

    it('should have correct spacing values', () => {
      expect(lightTheme.spacing).toEqual({
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
      });
    });

    it('should have correct border radius values', () => {
      expect(lightTheme.borderRadius).toEqual({
        sm: 4,
        md: 8,
        lg: 12,
      });
    });

    it('should have correct typography structure', () => {
      expect(lightTheme.typography).toHaveProperty('fontSize');
      expect(lightTheme.typography).toHaveProperty('fontWeight');

      expect(lightTheme.typography.fontSize).toEqual({
        xs: 12,
        sm: 14,
        md: 16,
        lg: 18,
        xl: 24,
        xxl: 32,
      });

      expect(lightTheme.typography.fontWeight).toEqual({
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      });
    });
  });

  describe('darkTheme', () => {
    it('should have same structure as light theme', () => {
      expect(darkTheme).toHaveProperty('colors');
      expect(darkTheme).toHaveProperty('spacing');
      expect(darkTheme).toHaveProperty('borderRadius');
      expect(darkTheme).toHaveProperty('typography');
    });

    it('should have same spacing as light theme', () => {
      expect(darkTheme.spacing).toEqual(lightTheme.spacing);
    });

    it('should have same border radius as light theme', () => {
      expect(darkTheme.borderRadius).toEqual(lightTheme.borderRadius);
    });

    it('should have same typography as light theme', () => {
      expect(darkTheme.typography).toEqual(lightTheme.typography);
    });

    it('should have different colors from light theme', () => {
      expect(darkTheme.colors).not.toEqual(lightTheme.colors);
      expect(darkTheme.colors.primary).not.toBe(lightTheme.colors.primary);
      expect(darkTheme.colors.background).not.toBe(lightTheme.colors.background);
    });
  });
});
