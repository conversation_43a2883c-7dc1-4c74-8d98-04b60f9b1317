import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile,
  deleteUser,
  signInWithCredential,
  PhoneAuthProvider,
} from 'firebase/auth';
import { authService } from '../auth';
import { mongoService } from '../mongodb';
import { UserProfile } from '../../types/auth';

// Mock Firebase Auth
jest.mock('firebase/auth');
jest.mock('../mongodb');

const mockSignInWithEmailAndPassword = signInWithEmailAndPassword as jest.MockedFunction<
  typeof signInWithEmailAndPassword
>;
const mockCreateUserWithEmailAndPassword = createUserWithEmailAndPassword as jest.MockedFunction<
  typeof createUserWithEmailAndPassword
>;
const mockSignOut = signOut as jest.MockedFunction<typeof signOut>;
const mockUpdateProfile = updateProfile as jest.MockedFunction<typeof updateProfile>;
const mockDeleteUser = deleteUser as jest.MockedFunction<typeof deleteUser>;
const mockSignInWithCredential = signInWithCredential as jest.MockedFunction<
  typeof signInWithCredential
>;
const mockPhoneAuthProvider = PhoneAuthProvider as jest.Mocked<typeof PhoneAuthProvider>;

const mockMongoService = mongoService as jest.Mocked<typeof mongoService>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('signInWithEmail', () => {
    it('should sign in user with email and password', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
      };

      mockSignInWithEmailAndPassword.mockResolvedValue({
        user: mockUser,
      } as any);

      mockMongoService.updateUserLastLogin.mockResolvedValue(undefined);

      const result = await authService.signInWithEmail('<EMAIL>', 'password123');

      expect(mockSignInWithEmailAndPassword).toHaveBeenCalledWith(
        expect.any(Object), // auth object
        '<EMAIL>',
        'password123',
      );
      expect(mockMongoService.updateUserLastLogin).toHaveBeenCalledWith('test-uid');
      expect(result).toEqual(mockUser);
    });

    it('should handle sign in errors', async () => {
      const error = new Error('Invalid credentials');
      (error as any).code = 'auth/wrong-password';

      mockSignInWithEmailAndPassword.mockRejectedValue(error);

      await expect(
        authService.signInWithEmail('<EMAIL>', 'wrongpassword'),
      ).rejects.toMatchObject({
        code: 'auth/wrong-password',
        message: 'Invalid credentials',
      });
    });
  });

  describe('signUpWithEmail', () => {
    it('should create new user with email and password', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
        displayName: null,
      };

      const profile: UserProfile = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      };

      mockCreateUserWithEmailAndPassword.mockResolvedValue({
        user: mockUser,
      } as any);

      mockUpdateProfile.mockResolvedValue(undefined);
      mockMongoService.createUser.mockResolvedValue({
        _id: 'mongo-id',
        authId: 'test-uid',
        profile,
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      });

      const result = await authService.signUpWithEmail(
        '<EMAIL>',
        'password123',
        profile,
      );

      expect(mockCreateUserWithEmailAndPassword).toHaveBeenCalledWith(
        expect.any(Object),
        '<EMAIL>',
        'password123',
      );
      expect(mockUpdateProfile).toHaveBeenCalledWith(mockUser, {
        displayName: 'Test User',
      });
      expect(mockMongoService.createUser).toHaveBeenCalled();
      expect(result).toEqual(mockUser);
    });

    it('should handle sign up errors', async () => {
      const error = new Error('Email already in use');
      (error as any).code = 'auth/email-already-in-use';

      mockCreateUserWithEmailAndPassword.mockRejectedValue(error);

      const profile: UserProfile = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      };

      await expect(
        authService.signUpWithEmail('<EMAIL>', 'password123', profile),
      ).rejects.toMatchObject({
        code: 'auth/email-already-in-use',
        message: 'Email already in use',
      });
    });
  });

  describe('verifyPhoneWithCredential', () => {
    it('should verify phone with credential for new user', async () => {
      const mockUser = {
        uid: 'test-uid',
        phoneNumber: '+**********',
      };

      const profile: UserProfile = {
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+**********',
      };

      mockPhoneAuthProvider.credential.mockReturnValue({} as any);
      mockSignInWithCredential.mockResolvedValue({
        user: mockUser,
      } as any);

      mockMongoService.getUserByAuthId.mockResolvedValue(null);
      mockMongoService.createUser.mockResolvedValue({
        _id: 'mongo-id',
        authId: 'test-uid',
        profile,
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      });

      const result = await authService.verifyPhoneWithCredential(
        'verification-id',
        '123456',
        profile,
      );

      expect(mockPhoneAuthProvider.credential).toHaveBeenCalledWith(
        'verification-id',
        '123456',
      );
      expect(mockSignInWithCredential).toHaveBeenCalled();
      expect(mockMongoService.getUserByAuthId).toHaveBeenCalledWith('test-uid');
      expect(mockMongoService.createUser).toHaveBeenCalled();
      expect(result).toEqual(mockUser);
    });

    it('should verify phone with credential for existing user', async () => {
      const mockUser = {
        uid: 'test-uid',
        phoneNumber: '+**********',
      };

      const existingMongoUser = {
        _id: 'mongo-id',
        authId: 'test-uid',
        profile: {
          firstName: 'Existing',
          lastName: 'User',
          phoneNumber: '+**********',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: true,
      };

      mockPhoneAuthProvider.credential.mockReturnValue({} as any);
      mockSignInWithCredential.mockResolvedValue({
        user: mockUser,
      } as any);

      mockMongoService.getUserByAuthId.mockResolvedValue(existingMongoUser);
      mockMongoService.updateUserLastLogin.mockResolvedValue(undefined);

      const result = await authService.verifyPhoneWithCredential('verification-id', '123456');

      expect(mockMongoService.getUserByAuthId).toHaveBeenCalledWith('test-uid');
      expect(mockMongoService.updateUserLastLogin).toHaveBeenCalledWith('test-uid');
      expect(mockMongoService.createUser).not.toHaveBeenCalled();
      expect(result).toEqual(mockUser);
    });
  });

  describe('signOut', () => {
    it('should sign out user', async () => {
      mockSignOut.mockResolvedValue(undefined);

      await authService.signOut();

      expect(mockSignOut).toHaveBeenCalledWith(expect.any(Object));
    });
  });

  describe('updateUserProfile', () => {
    it('should update user profile', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
      };

      // Mock auth.currentUser
      Object.defineProperty(require('../../config/firebase'), 'auth', {
        value: {
          currentUser: mockUser,
        },
        writable: true,
      });

      const profileUpdate = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      mockUpdateProfile.mockResolvedValue(undefined);
      mockMongoService.updateUser.mockResolvedValue({
        _id: 'mongo-id',
        authId: 'test-uid',
        profile: {
          firstName: 'Updated',
          lastName: 'Name',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      });

      await authService.updateUserProfile(profileUpdate);

      expect(mockUpdateProfile).toHaveBeenCalledWith(mockUser, {
        displayName: 'Updated Name',
      });
      expect(mockMongoService.updateUser).toHaveBeenCalledWith('test-uid', {
        profile: profileUpdate,
      });
    });

    it('should throw error when no authenticated user', async () => {
      // Mock auth.currentUser as null
      Object.defineProperty(require('../../config/firebase'), 'auth', {
        value: {
          currentUser: null,
        },
        writable: true,
      });

      await expect(
        authService.updateUserProfile({ firstName: 'Test' }),
      ).rejects.toThrow('No authenticated user');
    });
  });

  describe('deleteAccount', () => {
    it('should delete user account', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
      };

      const mongoUser = {
        _id: 'mongo-id',
        authId: 'test-uid',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      };

      // Mock auth.currentUser
      Object.defineProperty(require('../../config/firebase'), 'auth', {
        value: {
          currentUser: mockUser,
        },
        writable: true,
      });

      mockMongoService.getUserByAuthId.mockResolvedValue(mongoUser);
      mockMongoService.deleteUser.mockResolvedValue(true);
      mockDeleteUser.mockResolvedValue(undefined);

      await authService.deleteAccount();

      expect(mockMongoService.getUserByAuthId).toHaveBeenCalledWith('test-uid');
      expect(mockMongoService.deleteUser).toHaveBeenCalledWith('mongo-id');
      expect(mockDeleteUser).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('getMongoUser', () => {
    it('should get MongoDB user by auth ID', async () => {
      const mongoUser = {
        _id: 'mongo-id',
        authId: 'test-uid',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      };

      mockMongoService.getUserByAuthId.mockResolvedValue(mongoUser);

      const result = await authService.getMongoUser('test-uid');

      expect(mockMongoService.getUserByAuthId).toHaveBeenCalledWith('test-uid');
      expect(result).toEqual(mongoUser);
    });

    it('should handle errors gracefully', async () => {
      mockMongoService.getUserByAuthId.mockRejectedValue(new Error('Database error'));

      const result = await authService.getMongoUser('test-uid');

      expect(result).toBeNull();
    });
  });
});
