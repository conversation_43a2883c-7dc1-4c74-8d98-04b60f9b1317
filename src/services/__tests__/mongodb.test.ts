import { MongoClient, ObjectId } from 'mongodb';
import { mongoService } from '../mongodb';
import { UserDocument, CircleDocument } from '../../types/database';

// Mock MongoDB
jest.mock('mongodb');

const mockMongoClient = MongoClient as jest.MockedClass<typeof MongoClient>;
const mockObjectId = ObjectId as jest.MockedClass<typeof ObjectId>;

describe('MongoDBService', () => {
  let mockClient: any;
  let mockDb: any;
  let mockCollection: any;

  beforeEach(() => {
    jest.clearAllMocks();

    mockCollection = {
      findOne: jest.fn(),
      find: jest.fn(() => ({
        toArray: jest.fn(() => []),
        limit: jest.fn(() => ({
          toArray: jest.fn(() => []),
        })),
      })),
      insertOne: jest.fn(),
      updateOne: jest.fn(),
      deleteOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      createIndex: jest.fn(),
    };

    mockDb = {
      collection: jest.fn(() => mockCollection),
      admin: jest.fn(() => ({
        ping: jest.fn(),
      })),
    };

    mockClient = {
      connect: jest.fn(),
      close: jest.fn(),
      db: jest.fn(() => mockDb),
    };

    mockMongoClient.mockImplementation(() => mockClient);
    mockObjectId.mockImplementation(id => ({ _id: id || 'mock-object-id' }) as any);
  });

  describe('connect', () => {
    it('should connect to MongoDB', async () => {
      await mongoService.connect();

      expect(mockClient.connect).toHaveBeenCalled();
      expect(mockClient.db).toHaveBeenCalled();
    });

    it('should handle connection errors', async () => {
      const error = new Error('Connection failed');
      mockClient.connect.mockRejectedValue(error);

      await expect(mongoService.connect()).rejects.toThrow('Connection failed');
    });
  });

  describe('disconnect', () => {
    it('should disconnect from MongoDB', async () => {
      // First connect
      await mongoService.connect();

      // Then disconnect
      await mongoService.disconnect();

      expect(mockClient.close).toHaveBeenCalled();
    });
  });

  describe('createUser', () => {
    it('should create a new user', async () => {
      const userData: Omit<UserDocument, '_id' | 'createdAt' | 'updatedAt'> = {
        authId: 'test-auth-id',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      };

      const mockInsertResult = {
        insertedId: new ObjectId('507f1f77bcf86cd799439011'),
      };

      mockCollection.insertOne.mockResolvedValue(mockInsertResult);

      const result = await mongoService.createUser(userData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith(
        expect.objectContaining({
          ...userData,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        }),
      );

      expect(result).toMatchObject({
        ...userData,
        _id: mockInsertResult.insertedId,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });
  });

  describe('getUserByAuthId', () => {
    it('should find user by auth ID', async () => {
      const mockUser: UserDocument = {
        _id: 'user-id',
        authId: 'test-auth-id',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      };

      mockCollection.findOne.mockResolvedValue(mockUser);

      const result = await mongoService.getUserByAuthId('test-auth-id');

      expect(mockCollection.findOne).toHaveBeenCalledWith({ authId: 'test-auth-id' });
      expect(result).toEqual(mockUser);
    });

    it('should return null if user not found', async () => {
      mockCollection.findOne.mockResolvedValue(null);

      const result = await mongoService.getUserByAuthId('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('getUserById', () => {
    it('should find user by MongoDB ID', async () => {
      const mockUser: UserDocument = {
        _id: 'user-id',
        authId: 'test-auth-id',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      };

      mockCollection.findOne.mockResolvedValue(mockUser);

      const result = await mongoService.getUserById('user-id');

      expect(mockCollection.findOne).toHaveBeenCalledWith({ _id: expect.any(Object) });
      expect(result).toEqual(mockUser);
    });
  });

  describe('updateUser', () => {
    it('should update user and return updated document', async () => {
      const updates = {
        profile: {
          firstName: 'Updated',
          lastName: 'Name',
        },
      };

      const updatedUser: UserDocument = {
        _id: 'user-id',
        authId: 'test-auth-id',
        profile: {
          firstName: 'Updated',
          lastName: 'Name',
          email: '<EMAIL>',
        },
        locale: 'en',
        lastLogin: new Date(),
        circleIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        phoneVerified: false,
      };

      mockCollection.findOneAndUpdate.mockResolvedValue({ value: updatedUser });

      const result = await mongoService.updateUser('user-id', updates);

      expect(mockCollection.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: expect.any(Object) },
        { $set: expect.objectContaining({ ...updates, updatedAt: expect.any(Date) }) },
        { returnDocument: 'after' },
      );
      expect(result).toEqual(updatedUser);
    });
  });

  describe('updateUserLastLogin', () => {
    it('should update user last login timestamp', async () => {
      await mongoService.updateUserLastLogin('test-auth-id');

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { authId: 'test-auth-id' },
        {
          $set: {
            lastLogin: expect.any(Date),
            updatedAt: expect.any(Date),
          },
        },
      );
    });
  });

  describe('deleteUser', () => {
    it('should delete user and return true if successful', async () => {
      mockCollection.deleteOne.mockResolvedValue({ deletedCount: 1 });

      const result = await mongoService.deleteUser('user-id');

      expect(mockCollection.deleteOne).toHaveBeenCalledWith({ _id: expect.any(Object) });
      expect(result).toBe(true);
    });

    it('should return false if no user was deleted', async () => {
      mockCollection.deleteOne.mockResolvedValue({ deletedCount: 0 });

      const result = await mongoService.deleteUser('non-existent-id');

      expect(result).toBe(false);
    });
  });

  describe('getVerses', () => {
    it('should get verses with filters', async () => {
      const mockVerses = [
        {
          _id: 'verse-1',
          text: 'In the beginning was the Word',
          book: 'John',
          chapter: 1,
          verse: 1,
          translation: 'NIV',
          language: 'en',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockCollection.find.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockVerses),
      });

      const result = await mongoService.getVerses('John', 1, 1, 'NIV');

      expect(mockCollection.find).toHaveBeenCalledWith({
        book: 'John',
        chapter: 1,
        verse: 1,
        translation: 'NIV',
      });
      expect(result).toEqual(mockVerses);
    });

    it('should get verses without filters', async () => {
      const mockVerses = [];

      mockCollection.find.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockVerses),
      });

      const result = await mongoService.getVerses();

      expect(mockCollection.find).toHaveBeenCalledWith({});
      expect(result).toEqual(mockVerses);
    });
  });

  describe('searchVerses', () => {
    it('should search verses by text', async () => {
      const mockVerses = [
        {
          _id: 'verse-1',
          text: 'For God so loved the world',
          book: 'John',
          chapter: 3,
          verse: 16,
          translation: 'NIV',
          language: 'en',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockCollection.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          toArray: jest.fn().mockResolvedValue(mockVerses),
        }),
      });

      const result = await mongoService.searchVerses('love', 5);

      expect(mockCollection.find).toHaveBeenCalledWith({
        $text: { $search: 'love' },
      });
      expect(result).toEqual(mockVerses);
    });
  });

  describe('createCircle', () => {
    it('should create a new circle', async () => {
      const circleData: Omit<CircleDocument, '_id' | 'createdAt' | 'updatedAt'> = {
        name: 'Test Circle',
        description: 'A test circle',
        ownerId: 'owner-id',
        memberIds: [],
        settings: {
          visibility: 'public',
          allowMemberInvites: true,
          requireApproval: false,
        },
        isActive: true,
      };

      const mockInsertResult = {
        insertedId: new ObjectId('507f1f77bcf86cd799439012'),
      };

      mockCollection.insertOne.mockResolvedValue(mockInsertResult);

      const result = await mongoService.createCircle(circleData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith(
        expect.objectContaining({
          ...circleData,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        }),
      );

      expect(result).toMatchObject({
        ...circleData,
        _id: mockInsertResult.insertedId,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });
  });

  describe('getUserCircles', () => {
    it('should get circles for a user', async () => {
      const mockCircles = [
        {
          _id: 'circle-1',
          name: 'Test Circle',
          ownerId: 'user-id',
          memberIds: [],
          settings: {
            visibility: 'public',
            allowMemberInvites: true,
            requireApproval: false,
          },
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockCollection.find.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockCircles),
      });

      const result = await mongoService.getUserCircles('user-id');

      expect(mockCollection.find).toHaveBeenCalledWith({
        $or: [
          { ownerId: expect.any(Object) },
          { memberIds: expect.any(Object) },
        ],
        isActive: true,
      });
      expect(result).toEqual(mockCircles);
    });
  });

  describe('joinCircle', () => {
    it('should add user to circle members', async () => {
      mockCollection.updateOne.mockResolvedValue({ modifiedCount: 1 });

      const result = await mongoService.joinCircle('circle-id', 'user-id');

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { _id: expect.any(Object) },
        {
          $addToSet: { memberIds: expect.any(Object) },
          $set: { updatedAt: expect.any(Date) },
        },
      );
      expect(result).toBe(true);
    });

    it('should return false if no circle was modified', async () => {
      mockCollection.updateOne.mockResolvedValue({ modifiedCount: 0 });

      const result = await mongoService.joinCircle('non-existent-circle', 'user-id');

      expect(result).toBe(false);
    });
  });

  describe('healthCheck', () => {
    it('should return true if database is healthy', async () => {
      const result = await mongoService.healthCheck();

      expect(mockDb.admin().ping).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false if database is unhealthy', async () => {
      mockDb.admin().ping.mockRejectedValue(new Error('Connection failed'));

      const result = await mongoService.healthCheck();

      expect(result).toBe(false);
    });
  });

  describe('initializeDatabase', () => {
    it('should create database indexes', async () => {
      await mongoService.initializeDatabase();

      // Should create indexes for users collection
      expect(mockCollection.createIndex).toHaveBeenCalledWith(
        { authId: 1 },
        { unique: true },
      );
      expect(mockCollection.createIndex).toHaveBeenCalledWith({ 'profile.email': 1 });
      expect(mockCollection.createIndex).toHaveBeenCalledWith({ 'profile.phoneNumber': 1 });

      // Should create indexes for verses collection
      expect(mockCollection.createIndex).toHaveBeenCalledWith({
        book: 1,
        chapter: 1,
        verse: 1,
      });
      expect(mockCollection.createIndex).toHaveBeenCalledWith({ text: 'text' });

      // Should create indexes for circles collection
      expect(mockCollection.createIndex).toHaveBeenCalledWith({ ownerId: 1 });
      expect(mockCollection.createIndex).toHaveBeenCalledWith({ memberIds: 1 });
      expect(mockCollection.createIndex).toHaveBeenCalledWith({ isActive: 1 });
    });
  });
});
