import { MongoClient, Db, Collection, ObjectId } from 'mongodb';
import {
  UserDocument,
  VerseDocument,
  CircleDocument,
  DatabaseCollections,
  COLLECTION_NAMES,
} from '../types/database';

class MongoDBService {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private connectionString: string;
  private databaseName: string;

  constructor() {
    this.connectionString = process.env.EXPO_PUBLIC_MONGODB_URI || 'mongodb://localhost:27017';
    this.databaseName = process.env.EXPO_PUBLIC_MONGODB_DB_NAME || 'bible_companion';
  }

  async connect(): Promise<void> {
    try {
      if (!this.client) {
        this.client = new MongoClient(this.connectionString);
        await this.client.connect();
        this.db = this.client.db(this.databaseName);
        console.log('Connected to MongoDB');
      }
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.client) {
        await this.client.close();
        this.client = null;
        this.db = null;
        console.log('Disconnected from MongoDB');
      }
    } catch (error) {
      console.error('Failed to disconnect from MongoDB:', error);
      throw error;
    }
  }

  private getCollection<T extends keyof DatabaseCollections>(
    collectionName: T,
  ): Collection<DatabaseCollections[T]> {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db.collection<DatabaseCollections[T]>(collectionName);
  }

  // User operations
  async createUser(userData: Omit<UserDocument, '_id' | 'createdAt' | 'updatedAt'>): Promise<UserDocument> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.USERS);

    const user: UserDocument = {
      ...userData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await collection.insertOne(user);
    return { ...user, _id: result.insertedId };
  }

  async getUserByAuthId(authId: string): Promise<UserDocument | null> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.USERS);
    return await collection.findOne({ authId });
  }

  async getUserById(userId: string | ObjectId): Promise<UserDocument | null> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.USERS);
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    return await collection.findOne({ _id: objectId });
  }

  async updateUser(
    userId: string | ObjectId,
    updates: Partial<Omit<UserDocument, '_id' | 'createdAt'>>,
  ): Promise<UserDocument | null> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.USERS);
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;

    const updateData = {
      ...updates,
      updatedAt: new Date(),
    };

    const result = await collection.findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' },
    );

    return result.value;
  }

  async updateUserLastLogin(authId: string): Promise<void> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.USERS);

    await collection.updateOne(
      { authId },
      {
        $set: {
          lastLogin: new Date(),
          updatedAt: new Date(),
        },
      },
    );
  }

  async deleteUser(userId: string | ObjectId): Promise<boolean> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.USERS);
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;

    const result = await collection.deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }

  // Verse operations
  async getVerses(
    book?: string,
    chapter?: number,
    verse?: number,
    translation?: string,
  ): Promise<VerseDocument[]> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.VERSES);

    const filter: any = {};
    if (book) filter.book = book;
    if (chapter) filter.chapter = chapter;
    if (verse) filter.verse = verse;
    if (translation) filter.translation = translation;

    return await collection.find(filter).toArray();
  }

  async searchVerses(query: string, limit: number = 10): Promise<VerseDocument[]> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.VERSES);

    return await collection.find({
      $text: { $search: query },
    }).limit(limit).toArray();
  }

  // Circle operations
  async createCircle(circleData: Omit<CircleDocument, '_id' | 'createdAt' | 'updatedAt'>): Promise<CircleDocument> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.CIRCLES);

    const circle: CircleDocument = {
      ...circleData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await collection.insertOne(circle);
    return { ...circle, _id: result.insertedId };
  }

  async getUserCircles(userId: string | ObjectId): Promise<CircleDocument[]> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.CIRCLES);
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;

    return await collection.find({
      $or: [
        { ownerId: objectId },
        { memberIds: objectId },
      ],
      isActive: true,
    }).toArray();
  }

  async joinCircle(circleId: string | ObjectId, userId: string | ObjectId): Promise<boolean> {
    await this.connect();
    const collection = this.getCollection(COLLECTION_NAMES.CIRCLES);
    const circleObjectId = typeof circleId === 'string' ? new ObjectId(circleId) : circleId;
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;

    const result = await collection.updateOne(
      { _id: circleObjectId },
      {
        $addToSet: { memberIds: userObjectId },
        $set: { updatedAt: new Date() },
      },
    );

    return result.modifiedCount > 0;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.connect();
      await this.db?.admin().ping();
      return true;
    } catch (error) {
      console.error('MongoDB health check failed:', error);
      return false;
    }
  }

  // Initialize database with indexes
  async initializeDatabase(): Promise<void> {
    await this.connect();

    // Create indexes for better performance
    const usersCollection = this.getCollection(COLLECTION_NAMES.USERS);
    await usersCollection.createIndex({ authId: 1 }, { unique: true });
    await usersCollection.createIndex({ 'profile.email': 1 });
    await usersCollection.createIndex({ 'profile.phoneNumber': 1 });

    const versesCollection = this.getCollection(COLLECTION_NAMES.VERSES);
    await versesCollection.createIndex({ book: 1, chapter: 1, verse: 1 });
    await versesCollection.createIndex({ text: 'text' }); // Text search index

    const circlesCollection = this.getCollection(COLLECTION_NAMES.CIRCLES);
    await circlesCollection.createIndex({ ownerId: 1 });
    await circlesCollection.createIndex({ memberIds: 1 });
    await circlesCollection.createIndex({ isActive: 1 });

    console.log('Database indexes created successfully');
  }
}

// Singleton instance
export const mongoService = new MongoDBService();

// Export for testing
export { MongoDBService };
