import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../hooks/useTheme';
import { LanguagePicker } from './LanguagePicker';
import { supportedLanguages } from '../i18n';

export const LanguageButton: React.FC = () => {
  const { i18n } = useTranslation();
  const { theme } = useTheme();
  const [showLanguagePicker, setShowLanguagePicker] = useState(false);

  const currentLanguage = supportedLanguages.find(lang => lang.code === i18n.language);
  const displayText = currentLanguage?.code.toUpperCase() || 'EN';

  return (
    <>
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          },
        ]}
        onPress={() => setShowLanguagePicker(true)}
        activeOpacity={0.7}
      >
        <Text style={[styles.buttonText, { color: theme.colors.text }]}>
          {displayText}
        </Text>
      </TouchableOpacity>

      <LanguagePicker
        visible={showLanguagePicker}
        onClose={() => setShowLanguagePicker(false)}
      />
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    minWidth: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
