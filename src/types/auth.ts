import { User as FirebaseUser } from 'firebase/auth';

export interface AuthUser extends FirebaseUser {
  phoneNumber?: string | null;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  email?: string;
  phoneNumber?: string;
  avatar?: string;
  bio?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
  location?: {
    city?: string;
    country?: string;
    timezone?: string;
  };
  preferences?: {
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    privacy: {
      profileVisibility: 'public' | 'friends' | 'private';
      showEmail: boolean;
      showPhone: boolean;
    };
    reading: {
      preferredTranslation: string;
      fontSize: 'small' | 'medium' | 'large';
      theme: 'light' | 'dark' | 'auto';
    };
  };
}

export interface MongoUser {
  _id?: string;
  authId: string; // Firebase UID
  profile: UserProfile;
  locale: string;
  lastLogin: Date;
  circleIds: string[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  emailVerified: boolean;
  phoneVerified: boolean;
}

export interface AuthContextType {
  user: AuthUser | null;
  mongoUser: MongoUser | null;
  loading: boolean;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithEmail: (
    email: string,
    password: string,
    profile: Partial<UserProfile>
  ) => Promise<void>;
  signInWithPhone: (phoneNumber: string) => Promise<string>;
  verifyPhoneOTP: (verificationId: string, code: string, profile?: UserProfile) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  signInWithBiometrics: () => Promise<void>;
  enableBiometrics: () => Promise<boolean>;
  signOut: () => Promise<void>;
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;
  deleteAccount: () => Promise<void>;
}

export interface PhoneAuthState {
  verificationId: string | null;
  phoneNumber: string | null;
  isVerifying: boolean;
  error: string | null;
}

export interface AuthError {
  code: string;
  message: string;
  details?: string;
}

// Firebase Auth Error Codes
export const AUTH_ERROR_CODES = {
  INVALID_EMAIL: 'auth/invalid-email',
  USER_DISABLED: 'auth/user-disabled',
  USER_NOT_FOUND: 'auth/user-not-found',
  WRONG_PASSWORD: 'auth/wrong-password',
  EMAIL_ALREADY_IN_USE: 'auth/email-already-in-use',
  WEAK_PASSWORD: 'auth/weak-password',
  INVALID_PHONE_NUMBER: 'auth/invalid-phone-number',
  INVALID_VERIFICATION_CODE: 'auth/invalid-verification-code',
  INVALID_VERIFICATION_ID: 'auth/invalid-verification-id',
  TOO_MANY_REQUESTS: 'auth/too-many-requests',
  NETWORK_REQUEST_FAILED: 'auth/network-request-failed',
} as const;

export type AuthErrorCode = typeof AUTH_ERROR_CODES[
  keyof typeof AUTH_ERROR_CODES
];
