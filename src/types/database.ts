import { ObjectId } from 'mongodb';

// Base MongoDB document interface
export interface BaseDocument {
  _id?: ObjectId | string;
  createdAt: Date;
  updatedAt: Date;
}

// User Collection
export interface UserDocument extends BaseDocument {
  authId: string; // Firebase UID
  profile: {
    firstName: string;
    lastName: string;
    email?: string;
    phoneNumber?: string;
    avatar?: string;
    bio?: string;
    dateOfBirth?: Date;
    gender?: 'male' | 'female' | 'other';
    location?: {
      city?: string;
      country?: string;
      timezone?: string;
    };
    preferences?: {
      notifications: {
        email: boolean;
        push: boolean;
        sms: boolean;
      };
      privacy: {
        profileVisibility: 'public' | 'friends' | 'private';
        showEmail: boolean;
        showPhone: boolean;
      };
      reading: {
        preferredTranslation: string;
        fontSize: 'small' | 'medium' | 'large';
        theme: 'light' | 'dark' | 'auto';
      };
    };
  };
  locale: string;
  lastLogin: Date;
  circleIds: (ObjectId | string)[];
  isActive: boolean;
  emailVerified: boolean;
  phoneVerified: boolean;
}

// Verses Collection
export interface VerseDocument extends BaseDocument {
  text: string;
  book: string;
  chapter: number;
  verse: number;
  translation: string;
  language: string;
  embeddings?: number[]; // Vector embeddings for semantic search
  tags?: string[];
  metadata?: {
    testament: 'old' | 'new';
    bookOrder: number;
    verseCount: number;
  };
}

// Circles Collection (Bible study groups/communities)
export interface CircleDocument extends BaseDocument {
  name: string;
  description?: string;
  ownerId: ObjectId | string;
  memberIds: (ObjectId | string)[];
  settings: {
    visibility: 'public' | 'private' | 'invite-only';
    maxMembers?: number;
    allowMemberInvites: boolean;
    requireApproval: boolean;
  };
  tags?: string[];
  avatar?: string;
  isActive: boolean;
}

// Chat Threads Collection
export interface ChatThreadDocument extends BaseDocument {
  circleId: ObjectId | string;
  title?: string;
  type: 'general' | 'study' | 'prayer' | 'announcement';
  participantIds: (ObjectId | string)[];
  lastMessageId?: ObjectId | string;
  lastActivity: Date;
  isActive: boolean;
  metadata?: {
    verseReference?: {
      book: string;
      chapter: number;
      verse: number;
    };
    studyTopic?: string;
    prayerRequest?: boolean;
  };
}

// Messages Collection
export interface MessageDocument extends BaseDocument {
  threadId: ObjectId | string;
  senderId: ObjectId | string;
  content: {
    text?: string;
    type: 'text' | 'verse' | 'image' | 'audio' | 'file';
    attachments?: {
      url: string;
      type: string;
      size?: number;
      name?: string;
    }[];
    verseReference?: {
      book: string;
      chapter: number;
      verse: number;
      translation: string;
    };
  };
  reactions?: {
    userId: ObjectId | string;
    emoji: string;
    timestamp: Date;
  }[];
  replyToId?: ObjectId | string;
  editedAt?: Date;
  deletedAt?: Date;
  isRead: boolean;
}

// Reading Plans Collection
export interface ReadingPlanDocument extends BaseDocument {
  name: string;
  description: string;
  duration: number; // days
  verses: {
    day: number;
    references: {
      book: string;
      chapter: number;
      startVerse: number;
      endVerse: number;
    }[];
  }[];
  tags?: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  isPublic: boolean;
  createdBy: ObjectId | string;
}

// User Reading Progress Collection
export interface ReadingProgressDocument extends BaseDocument {
  userId: ObjectId | string;
  planId: ObjectId | string;
  currentDay: number;
  completedDays: number[];
  startDate: Date;
  targetEndDate: Date;
  actualEndDate?: Date;
  notes?: {
    day: number;
    content: string;
    timestamp: Date;
  }[];
  isCompleted: boolean;
}

// Prayer Requests Collection
export interface PrayerRequestDocument extends BaseDocument {
  userId: ObjectId | string;
  circleId?: ObjectId | string;
  title: string;
  description: string;
  category: 'personal' | 'family' | 'health' | 'work' | 'ministry' | 'other';
  privacy: 'public' | 'circle' | 'private';
  isAnswered: boolean;
  answeredAt?: Date;
  answerDescription?: string;
  prayedByIds: (ObjectId | string)[];
  tags?: string[];
}

// Database Collections Type Map
export interface DatabaseCollections {
  users: UserDocument;
  verses: VerseDocument;
  circles: CircleDocument;
  chat_threads: ChatThreadDocument;
  messages: MessageDocument;
  reading_plans: ReadingPlanDocument;
  reading_progress: ReadingProgressDocument;
  prayer_requests: PrayerRequestDocument;
}

// Collection Names
export const COLLECTION_NAMES = {
  USERS: 'users',
  VERSES: 'verses',
  CIRCLES: 'circles',
  CHAT_THREADS: 'chat_threads',
  MESSAGES: 'messages',
  READING_PLANS: 'reading_plans',
  READING_PROGRESS: 'reading_progress',
  PRAYER_REQUESTS: 'prayer_requests',
} as const;

export type CollectionName = typeof COLLECTION_NAMES[keyof typeof COLLECTION_NAMES];
