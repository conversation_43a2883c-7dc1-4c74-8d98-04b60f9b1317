import React from 'react';
import { render, waitFor, act } from '@testing-library/react-native';
import { useAuthState } from 'react-firebase-hooks/auth';
import { AuthProvider, useAuth } from '../AuthContext';
import { authService } from '../../services/auth';

// Mock the auth service
jest.mock('../../services/auth');
const mockAuthService = authService as jest.Mocked<typeof authService>;

// Mock react-firebase-hooks
const mockUseAuthState = useAuthState as jest.MockedFunction<typeof useAuthState>;

// Test component to access auth context
const TestComponent: React.FC = () => {
  useAuth();
  return null;
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuthState.mockReturnValue([null, false, null]);
    mockAuthService.getMongoUser.mockResolvedValue(null);
  });

  it('should provide auth context', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>,
    );

    // Should not throw error when accessing context
    expect(() => useAuth()).not.toThrow();
  });

  it('should throw error when used outside provider', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });

  it('should handle Firebase user state changes', async () => {
    const mockUser = {
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'Test User',
    };

    const mockMongoUser = {
      _id: 'mongo-id',
      authId: 'test-uid',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      },
      locale: 'en',
      lastLogin: new Date(),
      circleIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      emailVerified: true,
      phoneVerified: false,
    };

    mockUseAuthState.mockReturnValue([mockUser as any, false, null]);
    mockAuthService.getMongoUser.mockResolvedValue(mockMongoUser);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await waitFor(() => {
      expect(authContext.user).toEqual(mockUser);
      expect(authContext.mongoUser).toEqual(mockMongoUser);
      expect(authContext.loading).toBe(false);
    });

    expect(mockAuthService.getMongoUser).toHaveBeenCalledWith('test-uid');
  });

  it('should handle sign in with email', async () => {
    mockAuthService.signInWithEmail.mockResolvedValue({
      uid: 'test-uid',
      email: '<EMAIL>',
    } as any);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.signInWithEmail('<EMAIL>', 'password123');
    });

    expect(mockAuthService.signInWithEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      'password123',
    );
  });

  it('should handle sign up with email', async () => {
    const profile = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
    };

    mockAuthService.signUpWithEmail.mockResolvedValue({
      uid: 'test-uid',
      email: '<EMAIL>',
    } as any);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.signUpWithEmail('<EMAIL>', 'password123', profile);
    });

    expect(mockAuthService.signUpWithEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      'password123',
      profile,
    );
  });

  it('should handle phone authentication', async () => {
    mockAuthService.signInWithPhone.mockResolvedValue(undefined);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.signInWithPhone('+**********');
    });

    expect(mockAuthService.signInWithPhone).toHaveBeenCalledWith('+**********');
  });

  it('should handle OTP verification', async () => {
    mockAuthService.verifyPhoneWithCredential.mockResolvedValue({
      uid: 'test-uid',
      phoneNumber: '+**********',
    } as any);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.verifyPhoneOTP('verification-id', '123456');
    });

    expect(mockAuthService.verifyPhoneWithCredential).toHaveBeenCalledWith(
      'verification-id',
      '123456',
    );
  });

  it('should handle sign out', async () => {
    mockAuthService.signOut.mockResolvedValue(undefined);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.signOut();
    });

    expect(mockAuthService.signOut).toHaveBeenCalled();
  });

  it('should handle profile updates', async () => {
    const mockUser = {
      uid: 'test-uid',
      email: '<EMAIL>',
    };

    const updatedProfile = {
      firstName: 'Updated',
      lastName: 'Name',
    };

    const updatedMongoUser = {
      _id: 'mongo-id',
      authId: 'test-uid',
      profile: {
        firstName: 'Updated',
        lastName: 'Name',
        email: '<EMAIL>',
      },
      locale: 'en',
      lastLogin: new Date(),
      circleIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      emailVerified: true,
      phoneVerified: false,
    };

    mockUseAuthState.mockReturnValue([mockUser as any, false, null]);
    mockAuthService.updateUserProfile.mockResolvedValue(undefined);
    mockAuthService.getMongoUser.mockResolvedValue(updatedMongoUser);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.updateProfile(updatedProfile);
    });

    expect(mockAuthService.updateUserProfile).toHaveBeenCalledWith(updatedProfile);
    expect(mockAuthService.getMongoUser).toHaveBeenCalledWith('test-uid');
  });

  it('should handle account deletion', async () => {
    mockAuthService.deleteAccount.mockResolvedValue(undefined);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await act(async () => {
      await authContext.deleteAccount();
    });

    expect(mockAuthService.deleteAccount).toHaveBeenCalled();
  });

  it('should handle loading states correctly', async () => {
    // Start with loading state
    mockUseAuthState.mockReturnValue([null, true, null]);

    let authContext: any;
    const TestComponentWithContext: React.FC = () => {
      authContext = useAuth();
      return null;
    };

    const { rerender } = render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    // Should be loading initially
    expect(authContext.loading).toBe(true);

    // Simulate loading complete
    mockUseAuthState.mockReturnValue([null, false, null]);

    rerender(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>,
    );

    await waitFor(() => {
      expect(authContext.loading).toBe(false);
    });
  });
});
