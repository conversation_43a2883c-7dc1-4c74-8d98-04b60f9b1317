import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import AuthLoginScreen from '../auth-login';
import { useAuth } from '../../src/contexts/AuthContext';
import { useTheme } from '../../src/hooks/useTheme';

// Mock dependencies
jest.mock('expo-router', () => ({
  router: {
    back: jest.fn(),
  },
}));

jest.mock('../../src/contexts/AuthContext');
jest.mock('../../src/hooks/useTheme');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseTheme = useTheme as jest.MockedFunction<typeof useTheme>;
const mockRouter = router as jest.Mocked<typeof router>;

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('AuthLoginScreen', () => {
  const mockAuthContext = {
    user: null,
    mongoUser: null,
    loading: false,
    signInWithEmail: jest.fn(),
    signUpWithEmail: jest.fn(),
    signInWithPhone: jest.fn(),
    verifyPhoneOTP: jest.fn(),
    signOut: jest.fn(),
    updateProfile: jest.fn(),
    deleteAccount: jest.fn(),
  };

  const mockTheme = {
    colors: {
      primary: '#6366f1',
      secondary: '#8b5cf6',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      error: '#ef4444',
      warning: '#f59e0b',
      success: '#10b981',
    },
    spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 },
    borderRadius: { sm: 4, md: 8, lg: 12 },
    typography: {
      fontSize: { xs: 12, sm: 14, md: 16, lg: 18, xl: 24, xxl: 32 },
      fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue(mockAuthContext);
    mockUseTheme.mockReturnValue({ theme: mockTheme });
  });

  it('should render email login form by default', () => {
    render(<AuthLoginScreen />);

    expect(screen.getByText('Welcome Back')).toBeTruthy();
    expect(screen.getByText('Sign in to continue your Bible journey')).toBeTruthy();
    expect(screen.getByPlaceholderText('Email')).toBeTruthy();
    expect(screen.getByPlaceholderText('Password')).toBeTruthy();
    expect(screen.getByText('Sign In')).toBeTruthy();
  });

  it('should switch to phone authentication mode', () => {
    render(<AuthLoginScreen />);

    const phoneButton = screen.getByText('Phone');
    fireEvent.press(phoneButton);

    expect(screen.getByPlaceholderText('Phone Number (+1234567890)')).toBeTruthy();
    expect(screen.getByText('Please include your country code (e.g., +1 for US)')).toBeTruthy();
    expect(screen.getByText('Send Code')).toBeTruthy();
  });

  it('should validate email form inputs', async () => {
    render(<AuthLoginScreen />);

    const signInButton = screen.getByText('Sign In');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeTruthy();
      expect(screen.getByText('Password is required')).toBeTruthy();
    });
  });

  it('should validate email format', async () => {
    render(<AuthLoginScreen />);

    const emailInput = screen.getByPlaceholderText('Email');
    const signInButton = screen.getByText('Sign In');

    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeTruthy();
    });
  });

  it('should validate password length', async () => {
    render(<AuthLoginScreen />);

    const emailInput = screen.getByPlaceholderText('Email');
    const passwordInput = screen.getByPlaceholderText('Password');
    const signInButton = screen.getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, '123');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(screen.getByText('Password must be at least 6 characters')).toBeTruthy();
    });
  });

  it('should handle successful email login', async () => {
    mockAuthContext.signInWithEmail.mockResolvedValue(undefined);

    render(<AuthLoginScreen />);

    const emailInput = screen.getByPlaceholderText('Email');
    const passwordInput = screen.getByPlaceholderText('Password');
    const signInButton = screen.getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(mockAuthContext.signInWithEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'password123',
      );
    });

    expect(Alert.alert).toHaveBeenCalledWith(
      'Success',
      'Login successful!',
      expect.any(Array),
    );
  });

  it('should handle email login error', async () => {
    const error = { message: 'Invalid credentials' };
    mockAuthContext.signInWithEmail.mockRejectedValue(error);

    render(<AuthLoginScreen />);

    const emailInput = screen.getByPlaceholderText('Email');
    const passwordInput = screen.getByPlaceholderText('Password');
    const signInButton = screen.getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'wrongpassword');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Login Failed', 'Invalid credentials');
    });
  });

  it('should validate phone number format', async () => {
    render(<AuthLoginScreen />);

    const phoneButton = screen.getByText('Phone');
    fireEvent.press(phoneButton);

    const phoneInput = screen.getByPlaceholderText('Phone Number (+1234567890)');
    const sendCodeButton = screen.getByText('Send Code');

    fireEvent.changeText(phoneInput, 'invalid-phone');
    fireEvent.press(sendCodeButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid phone number with country code')).toBeTruthy();
    });
  });

  it('should handle phone authentication flow', async () => {
    mockAuthContext.signInWithPhone.mockResolvedValue(undefined);

    render(<AuthLoginScreen />);

    // Switch to phone mode
    const phoneButton = screen.getByText('Phone');
    fireEvent.press(phoneButton);

    // Enter phone number
    const phoneInput = screen.getByPlaceholderText('Phone Number (+1234567890)');
    const sendCodeButton = screen.getByText('Send Code');

    fireEvent.changeText(phoneInput, '+1234567890');
    fireEvent.press(sendCodeButton);

    await waitFor(() => {
      expect(mockAuthContext.signInWithPhone).toHaveBeenCalledWith('+1234567890');
    });

    // Should switch to OTP mode
    await waitFor(() => {
      expect(screen.getByText('Verify Phone')).toBeTruthy();
      expect(screen.getByText('Enter Verification Code')).toBeTruthy();
      expect(screen.getByPlaceholderText('000000')).toBeTruthy();
    });
  });

  it('should handle OTP verification', async () => {
    mockAuthContext.signInWithPhone.mockResolvedValue(undefined);
    mockAuthContext.verifyPhoneOTP.mockResolvedValue(undefined);

    render(<AuthLoginScreen />);

    // Switch to phone mode and send code
    const phoneButton = screen.getByText('Phone');
    fireEvent.press(phoneButton);

    const phoneInput = screen.getByPlaceholderText('Phone Number (+1234567890)');
    const sendCodeButton = screen.getByText('Send Code');

    fireEvent.changeText(phoneInput, '+1234567890');
    fireEvent.press(sendCodeButton);

    // Wait for OTP screen
    await waitFor(() => {
      expect(screen.getByText('Enter Verification Code')).toBeTruthy();
    });

    // Enter OTP and verify
    const otpInput = screen.getByPlaceholderText('000000');
    const verifyButton = screen.getByText('Verify Code');

    fireEvent.changeText(otpInput, '123456');
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(mockAuthContext.verifyPhoneOTP).toHaveBeenCalledWith(
        'demo-verification-id',
        '123456',
      );
    });
  });

  it('should validate OTP code length', async () => {
    render(<AuthLoginScreen />);

    // Switch to phone mode and send code
    const phoneButton = screen.getByText('Phone');
    fireEvent.press(phoneButton);

    const phoneInput = screen.getByPlaceholderText('Phone Number (+1234567890)');
    const sendCodeButton = screen.getByText('Send Code');

    fireEvent.changeText(phoneInput, '+1234567890');
    fireEvent.press(sendCodeButton);

    // Wait for OTP screen
    await waitFor(() => {
      expect(screen.getByText('Enter Verification Code')).toBeTruthy();
    });

    // Enter invalid OTP
    const otpInput = screen.getByPlaceholderText('000000');
    const verifyButton = screen.getByText('Verify Code');

    fireEvent.changeText(otpInput, '123');
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Verification code must be 6 digits')).toBeTruthy();
    });
  });

  it('should handle back navigation', () => {
    render(<AuthLoginScreen />);

    const backButton = screen.getByText('Back to Home');
    fireEvent.press(backButton);

    expect(mockRouter.back).toHaveBeenCalled();
  });

  it('should show loading state during authentication', async () => {
    mockAuthContext.signInWithEmail.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000)),
    );

    render(<AuthLoginScreen />);

    const emailInput = screen.getByPlaceholderText('Email');
    const passwordInput = screen.getByPlaceholderText('Password');
    const signInButton = screen.getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });
  });

  it('should clear errors when user starts typing', async () => {
    render(<AuthLoginScreen />);

    const emailInput = screen.getByPlaceholderText('Email');
    const signInButton = screen.getByText('Sign In');

    // Trigger validation error
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeTruthy();
    });

    // Start typing to clear error
    fireEvent.changeText(emailInput, '<EMAIL>');

    // Error should be cleared (this is implementation dependent)
    // The error might still be visible until form is revalidated
  });

  it('should handle resend code functionality', async () => {
    mockAuthContext.signInWithPhone.mockResolvedValue(undefined);

    render(<AuthLoginScreen />);

    // Navigate to OTP screen
    const phoneButton = screen.getByText('Phone');
    fireEvent.press(phoneButton);

    const phoneInput = screen.getByPlaceholderText('Phone Number (+1234567890)');
    const sendCodeButton = screen.getByText('Send Code');

    fireEvent.changeText(phoneInput, '+1234567890');
    fireEvent.press(sendCodeButton);

    await waitFor(() => {
      expect(screen.getByText('Resend Code')).toBeTruthy();
    });

    // Press resend code
    const resendButton = screen.getByText('Resend Code');
    fireEvent.press(resendButton);

    // Should go back to phone input
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Phone Number (+1234567890)')).toBeTruthy();
    });
  });
});
