import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '../src/hooks/useTheme';
import { useAuth } from '../src/contexts/AuthContext';
import { formatPhoneNumber, validateEmail, validatePhoneNumber } from '../src/config/firebase';
import { AuthError } from '../src/types/auth';

type AuthMode = 'email' | 'phone' | 'otp';

const AuthLoginScreen = (): JSX.Element => {
  const { theme } = useTheme();
  const { signInWithEmail, signInWithPhone, verifyPhoneOTP, loading } = useAuth();

  // Form state
  const [authMode, setAuthMode] = useState<AuthMode>('email');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [verificationId, setVerificationId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const clearErrors = (): void => {
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (authMode === 'email') {
      if (!email) {
        newErrors.email = 'Email is required';
      } else if (!validateEmail(email)) {
        newErrors.email = 'Please enter a valid email address';
      }

      if (!password) {
        newErrors.password = 'Password is required';
      } else if (password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }
    } else if (authMode === 'phone') {
      if (!phoneNumber) {
        newErrors.phoneNumber = 'Phone number is required';
      } else if (!validatePhoneNumber(formatPhoneNumber(phoneNumber))) {
        newErrors.phoneNumber = 'Please enter a valid phone number with country code';
      }
    } else if (authMode === 'otp') {
      if (!otpCode) {
        newErrors.otpCode = 'Verification code is required';
      } else if (otpCode.length !== 6) {
        newErrors.otpCode = 'Verification code must be 6 digits';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleEmailLogin = async (): Promise<void> => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      clearErrors();

      await signInWithEmail(email, password);

      Alert.alert('Success', 'Login successful!', [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Login Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneLogin = async (): Promise<void> => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      clearErrors();

      const formattedPhone = formatPhoneNumber(phoneNumber);

      // Note: This is a simplified implementation
      // In a real app, you'd handle the confirmation result
      await signInWithPhone(formattedPhone);

      // For demo purposes, we'll simulate getting a verification ID
      setVerificationId('demo-verification-id');
      setAuthMode('otp');

      Alert.alert(
        'Verification Code Sent',
        `A verification code has been sent to ${formattedPhone}`,
      );
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Phone Verification Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOTPVerification = async (): Promise<void> => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      clearErrors();

      await verifyPhoneOTP(verificationId, otpCode);

      Alert.alert('Success', 'Phone verification successful!', [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Verification Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (): void => {
    switch (authMode) {
      case 'email':
        handleEmailLogin();
        break;
      case 'phone':
        handlePhoneLogin();
        break;
      case 'otp':
        handleOTPVerification();
        break;
    }
  };

  const renderAuthModeSelector = (): JSX.Element => (
    <View style={styles.authModeSelector}>
      <TouchableOpacity
        style={[
          styles.authModeButton,
          authMode === 'email' && { backgroundColor: theme.colors.primary },
          authMode === 'email' && styles.authModeButtonActive,
        ]}
        onPress={() => setAuthMode('email')}
      >
        <Text
          style={[
            styles.authModeButtonText,
            { color: authMode === 'email' ? '#FFFFFF' : theme.colors.text },
          ]}
        >
          Email
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.authModeButton,
          authMode === 'phone' && { backgroundColor: theme.colors.primary },
          authMode === 'phone' && styles.authModeButtonActive,
        ]}
        onPress={() => setAuthMode('phone')}
      >
        <Text
          style={[
            styles.authModeButtonText,
            { color: authMode === 'phone' ? '#FFFFFF' : theme.colors.text },
          ]}
        >
          Phone
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmailForm = (): JSX.Element => (
    <View style={styles.form}>
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: theme.colors.surface,
            borderColor: errors.email ? theme.colors.error : theme.colors.border,
            color: theme.colors.text,
          },
        ]}
        placeholder="Email"
        placeholderTextColor={theme.colors.textSecondary}
        value={email}
        onChangeText={text => {
          setEmail(text);
          if (errors.email) clearErrors();
        }}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      {errors.email && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {errors.email}
        </Text>
      )}

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: theme.colors.surface,
            borderColor: errors.password ? theme.colors.error : theme.colors.border,
            color: theme.colors.text,
          },
        ]}
        placeholder="Password"
        placeholderTextColor={theme.colors.textSecondary}
        value={password}
        onChangeText={text => {
          setPassword(text);
          if (errors.password) clearErrors();
        }}
        secureTextEntry
      />
      {errors.password && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {errors.password}
        </Text>
      )}
    </View>
  );

  const renderPhoneForm = (): JSX.Element => (
    <View style={styles.form}>
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: theme.colors.surface,
            borderColor: errors.phoneNumber ? theme.colors.error : theme.colors.border,
            color: theme.colors.text,
          },
        ]}
        placeholder="Phone Number (+1234567890)"
        placeholderTextColor={theme.colors.textSecondary}
        value={phoneNumber}
        onChangeText={text => {
          setPhoneNumber(text);
          if (errors.phoneNumber) clearErrors();
        }}
        keyboardType="phone-pad"
        autoCapitalize="none"
        autoCorrect={false}
      />
      {errors.phoneNumber && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {errors.phoneNumber}
        </Text>
      )}

      <Text style={[styles.helperText, { color: theme.colors.textSecondary }]}>
        Please include your country code (e.g., +1 for US)
      </Text>
    </View>
  );

  const renderOTPForm = (): JSX.Element => (
    <View style={styles.form}>
      <Text style={[styles.otpTitle, { color: theme.colors.text }]}>
        Enter Verification Code
      </Text>
      <Text style={[styles.otpSubtitle, { color: theme.colors.textSecondary }]}>
        We sent a 6-digit code to {phoneNumber}
      </Text>

      <TextInput
        style={[
          styles.input,
          styles.otpInput,
          {
            backgroundColor: theme.colors.surface,
            borderColor: errors.otpCode ? theme.colors.error : theme.colors.border,
            color: theme.colors.text,
          },
        ]}
        placeholder="000000"
        placeholderTextColor={theme.colors.textSecondary}
        value={otpCode}
        onChangeText={text => {
          setOtpCode(text.replace(/[^0-9]/g, ''));
          if (errors.otpCode) clearErrors();
        }}
        keyboardType="numeric"
        maxLength={6}
        textAlign="center"
      />
      {errors.otpCode && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {errors.otpCode}
        </Text>
      )}

      <TouchableOpacity
        style={styles.resendButton}
        onPress={() => setAuthMode('phone')}
      >
        <Text style={[styles.resendButtonText, { color: theme.colors.primary }]}>
          Resend Code
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {authMode === 'otp' ? 'Verify Phone' : 'Welcome Back'}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {authMode === 'otp'
              ? 'Enter the verification code sent to your phone'
              : 'Sign in to continue your Bible journey'
            }
          </Text>

          {authMode !== 'otp' && renderAuthModeSelector()}

          {authMode === 'email' && renderEmailForm()}
          {authMode === 'phone' && renderPhoneForm()}
          {authMode === 'otp' && renderOTPForm()}

          <TouchableOpacity
            style={[
              styles.button,
              { backgroundColor: theme.colors.primary },
              (isLoading || loading) && styles.buttonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={isLoading || loading}
          >
            {(isLoading || loading) ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.buttonText}>
                {authMode === 'email' && 'Sign In'}
                {authMode === 'phone' && 'Send Code'}
                {authMode === 'otp' && 'Verify Code'}
              </Text>
            )}
          </TouchableOpacity>

          {authMode === 'otp' && (
            <TouchableOpacity
              style={styles.backToPhoneButton}
              onPress={() => setAuthMode('phone')}
            >
              <Text style={[styles.backButtonText, { color: theme.colors.primary }]}>
                Change Phone Number
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={[styles.backButtonText, { color: theme.colors.primary }]}>
              Back to Home
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.8,
  },
  authModeSelector: {
    flexDirection: 'row',
    marginBottom: 24,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 4,
  },
  authModeButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  authModeButtonActive: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  authModeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  form: {
    marginBottom: 24,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    marginTop: -12,
    marginBottom: 8,
    marginLeft: 4,
  },
  helperText: {
    fontSize: 14,
    marginTop: -12,
    marginBottom: 8,
    marginLeft: 4,
    fontStyle: 'italic',
  },
  otpTitle: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  otpSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.8,
  },
  otpInput: {
    fontSize: 24,
    fontWeight: '600',
    letterSpacing: 8,
  },
  resendButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  resendButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  button: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    minHeight: 56,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  backToPhoneButton: {
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default AuthLoginScreen;
