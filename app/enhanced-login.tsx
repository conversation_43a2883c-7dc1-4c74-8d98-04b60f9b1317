import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '../src/hooks/useTheme';
import { useAuth } from '../src/contexts/AuthContext';
import { formatPhoneNumber, validateEmail, validatePhoneNumber } from '../src/config/firebase';
import { AuthError, UserProfile } from '../src/types/auth';

type AuthTab = 'email' | 'phone' | 'social';
type AuthMode = 'signin' | 'signup';

const EnhancedLoginScreen = (): JSX.Element => {
  const { theme } = useTheme();
  const {
    signInWithEmail,
    signUpWithEmail,
    signInWithPhone,
    verifyPhoneOTP,
    signInWithGoogle,
    signInWithApple,
    signInWithBiometrics,
    loading,
  } = useAuth();

  // Tab and mode state
  const [activeTab, setActiveTab] = useState<AuthTab>('email');
  const [authMode, setAuthMode] = useState<AuthMode>('signin');

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [verificationId, setVerificationId] = useState('');

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const clearErrors = (): void => {
    setErrors({});
  };

  const validateEmailForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (authMode === 'signup') {
      if (!firstName) {
        newErrors.firstName = 'First name is required';
      }
      if (!lastName) {
        newErrors.lastName = 'Last name is required';
      }
      if (!confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (password !== confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePhoneForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!phoneNumber) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!validatePhoneNumber(phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    if (showOtpInput && !otpCode) {
      newErrors.otpCode = 'Verification code is required';
    }

    if (showOtpInput && authMode === 'signup') {
      if (!firstName) {
        newErrors.firstName = 'First name is required';
      }
      if (!lastName) {
        newErrors.lastName = 'Last name is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleEmailAuth = async (): Promise<void> => {
    if (!validateEmailForm()) return;

    try {
      setIsLoading(true);
      clearErrors();

      if (authMode === 'signin') {
        await signInWithEmail(email, password);
      } else {
        const profile: Partial<UserProfile> = {
          firstName,
          lastName,
          email,
        };
        await signUpWithEmail(email, password, profile);
      }

      Alert.alert(
        'Success',
        `${authMode === 'signin' ? 'Sign in' : 'Sign up'} successful!`,
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ],
      );
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Authentication Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneAuth = async (): Promise<void> => {
    if (!showOtpInput) {
      // Send OTP
      if (!validatePhoneForm()) return;

      try {
        setIsLoading(true);
        clearErrors();

        const phoneVerificationId = await signInWithPhone(phoneNumber);
        setVerificationId(phoneVerificationId);
        setShowOtpInput(true);
        Alert.alert(
          'OTP Sent',
          'Please check your phone for the verification code.',
        );
      } catch (error) {
        const authError = error as AuthError;
        Alert.alert('Phone Authentication Failed', authError.message);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Verify OTP
      if (!validatePhoneForm()) return;

      try {
        setIsLoading(true);
        clearErrors();

        const profile: UserProfile | undefined = authMode === 'signup' ? {
          firstName,
          lastName,
          phoneNumber,
          email: '',
          dateOfBirth: undefined,
          gender: undefined,
          location: undefined,
          bio: undefined,
          preferences: {
            notifications: {
              email: true,
              push: true,
              sms: false,
            },
            privacy: {
              profileVisibility: 'public',
              showEmail: false,
              showPhone: false,
            },
            reading: {
              preferredTranslation: 'NIV',
              fontSize: 'medium',
              theme: 'auto',
            },
          },
        } : undefined;

        await verifyPhoneOTP(verificationId, otpCode, profile);

        Alert.alert('Success', 'Phone verification successful!', [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]);
      } catch (error) {
        const authError = error as AuthError;
        Alert.alert('Verification Failed', authError.message);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleGoogleSignIn = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await signInWithGoogle();
      Alert.alert('Success', 'Google sign-in successful!', [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Google Sign-In Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAppleSignIn = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await signInWithApple();
      Alert.alert('Success', 'Apple sign-in successful!', [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Apple Sign-In Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricSignIn = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await signInWithBiometrics();
      Alert.alert('Success', 'Biometric sign-in successful!', [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      const authError = error as AuthError;
      Alert.alert('Biometric Sign-In Failed', authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  const renderTabButton = (tab: AuthTab, label: string) => (
    <TouchableOpacity
      key={tab}
      style={[
        styles.tabButton,
        { borderBottomColor: theme.colors.primary },
        activeTab === tab && styles.activeTab,
      ]}
      onPress={() => {
        setActiveTab(tab);
        clearErrors();
        setShowOtpInput(false);
      }}
    >
      <Text
        style={[
          styles.tabButtonText,
          { color: activeTab === tab ? theme.colors.primary : theme.colors.textSecondary },
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderModeToggle = () => (
    <View style={styles.modeToggle}>
      <TouchableOpacity
        style={[
          styles.modeButton,
          { backgroundColor: authMode === 'signin' ? theme.colors.primary : 'transparent' },
        ]}
        onPress={() => {
          setAuthMode('signin');
          clearErrors();
        }}
      >
        <Text
          style={[
            styles.modeButtonText,
            { color: authMode === 'signin' ? theme.colors.surface : theme.colors.text },
          ]}
        >
          Sign In
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.modeButton,
          { backgroundColor: authMode === 'signup' ? theme.colors.primary : 'transparent' },
        ]}
        onPress={() => {
          setAuthMode('signup');
          clearErrors();
        }}
      >
        <Text
          style={[
            styles.modeButtonText,
            { color: authMode === 'signup' ? theme.colors.surface : theme.colors.text },
          ]}
        >
          Sign Up
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmailForm = () => (
    <View style={styles.formContainer}>
      {authMode === 'signup' && (
        <>
          <View style={styles.nameRow}>
            <View style={[styles.nameInput, { marginRight: 8 }]}>
              <TextInput
                style={[
                  styles.input,
                  { color: theme.colors.text, borderColor: theme.colors.border },
                ]}
                placeholder="First Name"
                placeholderTextColor={theme.colors.textSecondary}
                value={firstName}
                onChangeText={setFirstName}
                autoCapitalize="words"
              />
              {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}
            </View>
            <View style={[styles.nameInput, { marginLeft: 8 }]}>
              <TextInput
                style={[
                  styles.input,
                  { color: theme.colors.text, borderColor: theme.colors.border },
                ]}
                placeholder="Last Name"
                placeholderTextColor={theme.colors.textSecondary}
                value={lastName}
                onChangeText={setLastName}
                autoCapitalize="words"
              />
              {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}
            </View>
          </View>
        </>
      )}

      <TextInput
        style={[styles.input, { color: theme.colors.text, borderColor: theme.colors.border }]}
        placeholder="Email"
        placeholderTextColor={theme.colors.textSecondary}
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}

      <TextInput
        style={[styles.input, { color: theme.colors.text, borderColor: theme.colors.border }]}
        placeholder="Password"
        placeholderTextColor={theme.colors.textSecondary}
        value={password}
        onChangeText={setPassword}
        secureTextEntry
        autoCapitalize="none"
      />
      {errors.password && <Text style={styles.errorText}>{errors.password}</Text>}

      {authMode === 'signup' && (
        <>
          <TextInput
            style={[styles.input, { color: theme.colors.text, borderColor: theme.colors.border }]}
            placeholder="Confirm Password"
            placeholderTextColor={theme.colors.textSecondary}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            autoCapitalize="none"
          />
          {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword}</Text>}
        </>
      )}

      <TouchableOpacity
        style={[
          styles.authButton,
          { backgroundColor: theme.colors.primary },
          (isLoading || loading) && styles.disabledButton,
        ]}
        onPress={handleEmailAuth}
        disabled={isLoading || loading}
      >
        {isLoading || loading ? (
          <ActivityIndicator color={theme.colors.surface} />
        ) : (
          <Text style={[styles.authButtonText, { color: theme.colors.surface }]}>
            {authMode === 'signin' ? 'Sign In' : 'Sign Up'}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderPhoneForm = () => (
    <View style={styles.formContainer}>
      {authMode === 'signup' && !showOtpInput && (
        <>
          <View style={styles.nameRow}>
            <View style={[styles.nameInput, { marginRight: 8 }]}>
              <TextInput
                style={[
                  styles.input,
                  { color: theme.colors.text, borderColor: theme.colors.border },
                ]}
                placeholder="First Name"
                placeholderTextColor={theme.colors.textSecondary}
                value={firstName}
                onChangeText={setFirstName}
                autoCapitalize="words"
              />
              {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}
            </View>
            <View style={[styles.nameInput, { marginLeft: 8 }]}>
              <TextInput
                style={[
                  styles.input,
                  { color: theme.colors.text, borderColor: theme.colors.border },
                ]}
                placeholder="Last Name"
                placeholderTextColor={theme.colors.textSecondary}
                value={lastName}
                onChangeText={setLastName}
                autoCapitalize="words"
              />
              {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}
            </View>
          </View>
        </>
      )}

      {!showOtpInput ? (
        <>
          <TextInput
            style={[
              styles.input,
              { color: theme.colors.text, borderColor: theme.colors.border },
            ]}
            placeholder="Phone Number (+1234567890)"
            placeholderTextColor={theme.colors.textSecondary}
            value={phoneNumber}
            onChangeText={text => setPhoneNumber(formatPhoneNumber(text))}
            keyboardType="phone-pad"
          />
          {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber}</Text>}
        </>
      ) : (
        <>
          <Text style={[styles.otpLabel, { color: theme.colors.text }]}>
            Enter the verification code sent to {phoneNumber}
          </Text>
          <TextInput
            style={[
              styles.input,
              { color: theme.colors.text, borderColor: theme.colors.border },
            ]}
            placeholder="Verification Code"
            placeholderTextColor={theme.colors.textSecondary}
            value={otpCode}
            onChangeText={setOtpCode}
            keyboardType="number-pad"
            maxLength={6}
          />
          {errors.otpCode && <Text style={styles.errorText}>{errors.otpCode}</Text>}
        </>
      )}

      <TouchableOpacity
        style={[
          styles.authButton,
          { backgroundColor: theme.colors.primary },
          (isLoading || loading) && styles.disabledButton,
        ]}
        onPress={handlePhoneAuth}
        disabled={isLoading || loading}
      >
        {isLoading || loading ? (
          <ActivityIndicator color={theme.colors.surface} />
        ) : (
          <Text style={[styles.authButtonText, { color: theme.colors.surface }]}>
            {!showOtpInput ? 'Send Code' : 'Verify Code'}
          </Text>
        )}
      </TouchableOpacity>

      {showOtpInput && (
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            setShowOtpInput(false);
            setOtpCode('');
            clearErrors();
          }}
        >
          <Text style={[styles.backButtonText, { color: theme.colors.primary }]}>
            Back to Phone Number
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSocialForm = () => (
    <View style={styles.formContainer}>
      <Text style={[styles.socialTitle, { color: theme.colors.text }]}>
        Sign in with your preferred method
      </Text>

      <TouchableOpacity
        style={[
          styles.socialButton,
          { backgroundColor: '#4285F4', borderColor: '#4285F4' },
          (isLoading || loading) && styles.disabledButton,
        ]}
        onPress={handleGoogleSignIn}
        disabled={isLoading || loading}
      >
        {isLoading || loading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={[styles.socialButtonText, { color: '#FFFFFF' }]}>
            Continue with Google
          </Text>
        )}
      </TouchableOpacity>

      {Platform.OS === 'ios' && (
        <TouchableOpacity
          style={[
            styles.socialButton,
            { backgroundColor: '#000000', borderColor: '#000000' },
            (isLoading || loading) && styles.disabledButton,
          ]}
          onPress={handleAppleSignIn}
          disabled={isLoading || loading}
        >
          {isLoading || loading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={[styles.socialButtonText, { color: '#FFFFFF' }]}>
              Continue with Apple
            </Text>
          )}
        </TouchableOpacity>
      )}

      <TouchableOpacity
        style={[
          styles.socialButton,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          },
          (isLoading || loading) && styles.disabledButton,
        ]}
        onPress={handleBiometricSignIn}
        disabled={isLoading || loading}
      >
        {isLoading || loading ? (
          <ActivityIndicator color={theme.colors.primary} />
        ) : (
          <Text style={[styles.socialButtonText, { color: theme.colors.text }]}>
            Use Biometric Authentication
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>Welcome</Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Continue your spiritual journey
          </Text>
        </View>

        {/* Mode Toggle */}
        {activeTab !== 'social' && renderModeToggle()}

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          {renderTabButton('email', 'Email')}
          {renderTabButton('phone', 'Phone')}
          {renderTabButton('social', 'Social')}
        </View>

        {/* Form Content */}
        <View style={styles.contentContainer}>
          {activeTab === 'email' && renderEmailForm()}
          {activeTab === 'phone' && renderPhoneForm()}
          {activeTab === 'social' && renderSocialForm()}
        </View>

        {/* Web Recaptcha Container */}
        {Platform.OS === 'web' && <div id="recaptcha-container"></div>}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  modeToggle: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 4,
    marginBottom: 20,
  },
  modeButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  modeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  formContainer: {
    gap: 16,
  },
  nameRow: {
    flexDirection: 'row',
  },
  nameInput: {
    flex: 1,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginTop: 4,
  },
  otpLabel: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  authButton: {
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  authButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  backButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  socialTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 20,
  },
  socialButton: {
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EnhancedLoginScreen;
