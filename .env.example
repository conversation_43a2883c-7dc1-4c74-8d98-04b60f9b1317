# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
EXPO_PUBLIC_FIREBASE_APP_ID=your-app-id

# MongoDB Configuration
EXPO_PUBLIC_MONGODB_URI=mongodb://localhost:27017
EXPO_PUBLIC_MONGODB_DB_NAME=bible_companion

# Optional: MongoDB Atlas Connection
# EXPO_PUBLIC_MONGODB_URI=mongodb+srv://username:<EMAIL>/
# EXPO_PUBLIC_MONGODB_DB_NAME=bible_companion_prod

# Development/Production Environment
NODE_ENV=development

# API Configuration (for future use)
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000/api
EXPO_PUBLIC_API_TIMEOUT=10000

# Feature Flags (for future use)
EXPO_PUBLIC_ENABLE_ANALYTICS=false
EXPO_PUBLIC_ENABLE_CRASH_REPORTING=false
EXPO_PUBLIC_ENABLE_PERFORMANCE_MONITORING=false

# Security Configuration
EXPO_PUBLIC_ENABLE_BIOMETRIC_AUTH=true
EXPO_PUBLIC_SESSION_TIMEOUT=86400000

# Bible API Configuration (for future use)
EXPO_PUBLIC_BIBLE_API_KEY=your-bible-api-key
EXPO_PUBLIC_DEFAULT_TRANSLATION=NIV
EXPO_PUBLIC_SUPPORTED_TRANSLATIONS=NIV,ESV,NASB,KJV,NLT

# Push Notifications (for future use)
EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=true
EXPO_PUBLIC_FIREBASE_VAPID_KEY=your-vapid-key

# Social Authentication (for future use)
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
EXPO_PUBLIC_APPLE_CLIENT_ID=your-apple-client-id
EXPO_PUBLIC_FACEBOOK_APP_ID=your-facebook-app-id

# Logging and Monitoring
EXPO_PUBLIC_LOG_LEVEL=info
EXPO_PUBLIC_ENABLE_DEBUG_LOGS=true
